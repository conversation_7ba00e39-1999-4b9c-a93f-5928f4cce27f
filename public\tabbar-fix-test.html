<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>Tabbar修复测试 - 随心听</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            overflow-x: hidden;
        }

        /* 模拟应用布局 */
        .app-layout {
            width: 100vw;
            height: 100vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        /* 搜索栏 */
        .search-bar {
            height: 80px; /* 包含padding */
            background: white;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            padding: 16px 20px;
            z-index: 100;
            flex-shrink: 0;
        }

        .search-input {
            flex: 1;
            height: 36px;
            border: 1px solid #ddd;
            border-radius: 18px;
            padding: 0 15px;
            background: #f8f8f8;
        }

        /* 主内容区域 - 关键修复 */
        .main-content {
            flex: 1;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding: 0 20px 20px 20px;
            /* 当显示tabbar时，需要为底部导航留出空间 */
            margin-bottom: 60px;
        }

        .main-content.no-tabbar {
            margin-bottom: 0;
        }

        /* TopBanner */
        .top-banner {
            height: 180px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            margin: 20px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            font-weight: bold;
        }

        /* 内容卡片 */
        .content-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .content-card h3 {
            margin-bottom: 15px;
            color: #333;
        }

        .content-item {
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .content-item:last-child {
            border-bottom: none;
        }

        /* 底部导航栏 */
        .bottom-nav {
            position: fixed;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100vw;
            height: 60px;
            padding-bottom: env(safe-area-inset-bottom, 0px);
            background: rgba(255, 255, 255, 0.98);
            z-index: 1000;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            box-shadow: 0 -1px 8px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .bottom-nav.hidden {
            display: none;
        }

        .nav-button {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: rgba(0, 0, 0, 0.6);
            font-size: 12px;
            padding: 8px 0;
            transition: all 0.3s;
            cursor: pointer;
            width: 25%;
        }

        .nav-button.active {
            color: #4CAF50;
            transform: scale(1.05);
            font-weight: 500;
        }

        .nav-icon {
            font-size: 24px;
            margin-bottom: 4px;
        }

        /* 控制按钮 */
        .controls {
            position: fixed;
            top: 90px;
            right: 10px;
            z-index: 200;
        }

        .control-btn {
            display: block;
            margin-bottom: 10px;
            padding: 8px 12px;
            background: rgba(0,0,0,0.8);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
        }

        .control-btn.active {
            background: #4CAF50;
        }

        .status-info {
            position: fixed;
            top: 90px;
            left: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 8px;
            font-size: 12px;
            z-index: 200;
            max-width: 200px;
        }
    </style>
</head>
<body>
    <div class="app-layout">
        <!-- 状态信息 -->
        <div class="status-info" id="statusInfo">
            <div>视口: <span id="viewport">-</span></div>
            <div>内容高度: <span id="contentHeight">-</span></div>
            <div>可滚动: <span id="scrollable">-</span></div>
            <div>Tabbar: <span id="tabbarStatus">显示</span></div>
        </div>

        <!-- 控制按钮 -->
        <div class="controls">
            <button class="control-btn active" id="toggleTabbar">隐藏Tabbar</button>
            <button class="control-btn" id="addContent">添加内容</button>
            <button class="control-btn" id="resetContent">重置内容</button>
        </div>

        <!-- 搜索栏 -->
        <div class="search-bar">
            <input type="text" class="search-input" placeholder="搜索点什么呢...">
            <button style="margin-left: 10px; padding: 8px 16px; border: none; background: #4CAF50; color: white; border-radius: 6px;">搜索</button>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content" id="mainContent">
            <!-- TopBanner -->
            <div class="top-banner">
                🎵 推荐音乐横幅
            </div>

            <div id="contentContainer">
                <!-- 内容卡片 -->
                <div class="content-card">
                    <h3>🎶 本周最热音乐</h3>
                    <div class="content-item">歌曲 1 - 艺术家 A</div>
                    <div class="content-item">歌曲 2 - 艺术家 B</div>
                    <div class="content-item">歌曲 3 - 艺术家 C</div>
                    <div class="content-item">歌曲 4 - 艺术家 D</div>
                    <div class="content-item">歌曲 5 - 艺术家 E</div>
                </div>

                <div class="content-card">
                    <h3>💿 推荐专辑</h3>
                    <div class="content-item">专辑 1 - 艺术家 A</div>
                    <div class="content-item">专辑 2 - 艺术家 B</div>
                    <div class="content-item">专辑 3 - 艺术家 C</div>
                    <div class="content-item">专辑 4 - 艺术家 D</div>
                </div>

                <div class="content-card">
                    <h3>❤️ 我的收藏</h3>
                    <div class="content-item">收藏歌曲 1</div>
                    <div class="content-item">收藏歌曲 2</div>
                    <div class="content-item">收藏歌曲 3</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="bottom-nav" id="bottomNav">
        <div class="nav-button active">
            <div class="nav-icon">🏠</div>
            <span>首页</span>
        </div>
        <div class="nav-button">
            <div class="nav-icon">🔍</div>
            <span>搜索</span>
        </div>
        <div class="nav-button">
            <div class="nav-icon">📄</div>
            <span>歌单</span>
        </div>
        <div class="nav-button">
            <div class="nav-icon">👤</div>
            <span>我的</span>
        </div>
    </div>

    <script>
        let tabbarVisible = true;
        let contentCount = 3;

        function updateStatus() {
            const viewport = `${window.innerWidth}×${window.innerHeight}`;
            const mainContent = document.getElementById('mainContent');
            const contentHeight = mainContent.scrollHeight;
            const isScrollable = contentHeight > mainContent.clientHeight;
            
            document.getElementById('viewport').textContent = viewport;
            document.getElementById('contentHeight').textContent = contentHeight + 'px';
            document.getElementById('scrollable').textContent = isScrollable ? '是' : '否';
            document.getElementById('tabbarStatus').textContent = tabbarVisible ? '显示' : '隐藏';
        }

        function toggleTabbar() {
            const bottomNav = document.getElementById('bottomNav');
            const mainContent = document.getElementById('mainContent');
            const toggleBtn = document.getElementById('toggleTabbar');
            
            tabbarVisible = !tabbarVisible;
            
            if (tabbarVisible) {
                bottomNav.classList.remove('hidden');
                mainContent.classList.remove('no-tabbar');
                toggleBtn.textContent = '隐藏Tabbar';
                toggleBtn.classList.add('active');
            } else {
                bottomNav.classList.add('hidden');
                mainContent.classList.add('no-tabbar');
                toggleBtn.textContent = '显示Tabbar';
                toggleBtn.classList.remove('active');
            }
            
            updateStatus();
        }

        function addContent() {
            const container = document.getElementById('contentContainer');
            contentCount++;
            
            const newCard = document.createElement('div');
            newCard.className = 'content-card';
            newCard.innerHTML = `
                <h3>🎵 新增内容 ${contentCount}</h3>
                <div class="content-item">内容项 1</div>
                <div class="content-item">内容项 2</div>
                <div class="content-item">内容项 3</div>
            `;
            
            container.appendChild(newCard);
            updateStatus();
        }

        function resetContent() {
            const container = document.getElementById('contentContainer');
            const cards = container.querySelectorAll('.content-card');
            
            // 只保留前3个卡片
            for (let i = 3; i < cards.length; i++) {
                cards[i].remove();
            }
            
            contentCount = 3;
            updateStatus();
        }

        // 初始化
        updateStatus();

        // 事件监听
        document.getElementById('toggleTabbar').addEventListener('click', toggleTabbar);
        document.getElementById('addContent').addEventListener('click', addContent);
        document.getElementById('resetContent').addEventListener('click', resetContent);

        window.addEventListener('resize', updateStatus);
        window.addEventListener('orientationchange', () => {
            setTimeout(updateStatus, 100);
        });

        // 导航按钮点击事件
        document.querySelectorAll('.nav-button').forEach((btn, index) => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.nav-button').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
            });
        });

        console.log('🔧 Tabbar修复测试页面已加载');
    </script>
</body>
</html>
