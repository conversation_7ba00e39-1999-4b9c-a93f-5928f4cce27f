<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>布局测试 - 随心听</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            overflow-x: hidden;
        }

        /* 模拟搜索栏 */
        .search-bar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: white;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            padding: 0 20px;
            z-index: 100;
        }

        .search-input {
            flex: 1;
            height: 36px;
            border: 1px solid #ddd;
            border-radius: 18px;
            padding: 0 15px;
            background: #f8f8f8;
        }

        /* 主内容区域 */
        .main-content {
            margin-top: 60px; /* 为搜索栏留出空间 */
            height: calc(100vh - 120px); /* 减去搜索栏和底部导航 */
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding: 20px;
            padding-bottom: 20px;
        }

        /* 模拟TopBanner */
        .top-banner {
            height: 180px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            font-weight: bold;
        }

        /* 内容卡片 */
        .content-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .content-card h3 {
            margin-bottom: 15px;
            color: #333;
        }

        .content-item {
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .content-item:last-child {
            border-bottom: none;
        }

        /* 底部导航栏 */
        .bottom-nav {
            position: fixed;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100vw;
            height: 60px;
            padding-bottom: env(safe-area-inset-bottom, 0px);
            background: rgba(255, 255, 255, 0.98);
            z-index: 1000;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            box-shadow: 0 -1px 8px rgba(0, 0, 0, 0.1);
        }

        .nav-button {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: rgba(0, 0, 0, 0.6);
            font-size: 12px;
            padding: 8px 0;
            transition: all 0.3s;
            cursor: pointer;
            width: 25%;
        }

        .nav-button.active {
            color: #4CAF50;
            transform: scale(1.05);
            font-weight: 500;
        }

        .nav-icon {
            font-size: 24px;
            margin-bottom: 4px;
        }

        .status-info {
            position: fixed;
            top: 70px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 8px;
            font-size: 12px;
            z-index: 200;
        }
    </style>
</head>
<body>
    <!-- 状态信息 -->
    <div class="status-info" id="statusInfo">
        视口: <span id="viewport">-</span><br>
        内容高度: <span id="contentHeight">-</span><br>
        滚动: <span id="scrollable">-</span>
    </div>

    <!-- 搜索栏 -->
    <div class="search-bar">
        <input type="text" class="search-input" placeholder="搜索点什么呢...">
        <button style="margin-left: 10px; padding: 8px 16px; border: none; background: #4CAF50; color: white; border-radius: 6px;">搜索</button>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content" id="mainContent">
        <!-- TopBanner -->
        <div class="top-banner">
            🎵 推荐音乐横幅
        </div>

        <!-- 内容卡片 -->
        <div class="content-card">
            <h3>🎶 本周最热音乐</h3>
            <div class="content-item">歌曲 1 - 艺术家 A</div>
            <div class="content-item">歌曲 2 - 艺术家 B</div>
            <div class="content-item">歌曲 3 - 艺术家 C</div>
            <div class="content-item">歌曲 4 - 艺术家 D</div>
            <div class="content-item">歌曲 5 - 艺术家 E</div>
        </div>

        <div class="content-card">
            <h3>💿 推荐专辑</h3>
            <div class="content-item">专辑 1 - 艺术家 A</div>
            <div class="content-item">专辑 2 - 艺术家 B</div>
            <div class="content-item">专辑 3 - 艺术家 C</div>
            <div class="content-item">专辑 4 - 艺术家 D</div>
        </div>

        <div class="content-card">
            <h3>❤️ 我的收藏</h3>
            <div class="content-item">收藏歌曲 1</div>
            <div class="content-item">收藏歌曲 2</div>
            <div class="content-item">收藏歌曲 3</div>
        </div>

        <div class="content-card">
            <h3>📻 电台推荐</h3>
            <div class="content-item">电台 1 - 流行音乐</div>
            <div class="content-item">电台 2 - 古典音乐</div>
            <div class="content-item">电台 3 - 摇滚音乐</div>
        </div>

        <div class="content-card">
            <h3>🎤 歌手推荐</h3>
            <div class="content-item">歌手 1</div>
            <div class="content-item">歌手 2</div>
            <div class="content-item">歌手 3</div>
            <div class="content-item">歌手 4</div>
            <div class="content-item">歌手 5</div>
        </div>

        <div class="content-card">
            <h3>🔥 热门榜单</h3>
            <div class="content-item">热门榜 1</div>
            <div class="content-item">热门榜 2</div>
            <div class="content-item">热门榜 3</div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="bottom-nav">
        <div class="nav-button active">
            <div class="nav-icon">🏠</div>
            <span>首页</span>
        </div>
        <div class="nav-button">
            <div class="nav-icon">🔍</div>
            <span>搜索</span>
        </div>
        <div class="nav-button">
            <div class="nav-icon">📄</div>
            <span>歌单</span>
        </div>
        <div class="nav-button">
            <div class="nav-icon">👤</div>
            <span>我的</span>
        </div>
    </div>

    <script>
        function updateStatus() {
            const viewport = `${window.innerWidth}×${window.innerHeight}`;
            const mainContent = document.getElementById('mainContent');
            const contentHeight = mainContent.scrollHeight;
            const isScrollable = contentHeight > mainContent.clientHeight;
            
            document.getElementById('viewport').textContent = viewport;
            document.getElementById('contentHeight').textContent = contentHeight + 'px';
            document.getElementById('scrollable').textContent = isScrollable ? '可滚动' : '不可滚动';
        }

        // 初始化
        updateStatus();

        // 监听变化
        window.addEventListener('resize', updateStatus);
        window.addEventListener('orientationchange', () => {
            setTimeout(updateStatus, 100);
        });

        // 导航按钮点击事件
        document.querySelectorAll('.nav-button').forEach((btn, index) => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.nav-button').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
            });
        });

        console.log('📱 布局测试页面已加载');
    </script>
</body>
</html>
