# 📱 iOS PWA 启动屏幕尺寸清单

## 🎯 概述

本文档提供了iOS PWA应用所需的所有启动屏幕图片的精确尺寸清单。请按照以下规格准备启动画面图片。

## 📐 完整尺寸清单

### 📱 iPhone 设备 - 竖屏 (Portrait)

| 设备型号 | 文件名 | 尺寸 (px) | 像素密度 | 备注 |
|---------|--------|-----------|----------|------|
| iPhone SE (1st gen) | `iphone-se-portrait.png` | 640×1136 | @2x | 4英寸屏幕 |
| iPhone 6/7/8 | `iphone-6-portrait.png` | 750×1334 | @2x | 4.7英寸屏幕 |
| iPhone 6/7/8 Plus | `iphone-6-plus-portrait.png` | 1242×2208 | @3x | 5.5英寸屏幕 |
| iPhone X/XS/11 Pro | `iphone-x-portrait.png` | 1125×2436 | @3x | 5.8英寸全面屏 |
| iPhone XR/11 | `iphone-xr-portrait.png` | 828×1792 | @2x | 6.1英寸全面屏 |
| iPhone XS Max/11 Pro Max | `iphone-xs-max-portrait.png` | 1242×2688 | @3x | 6.5英寸全面屏 |
| iPhone 12 mini | `iphone-12-mini-portrait.png` | 1080×2340 | @3x | 5.4英寸全面屏 |
| iPhone 12/12 Pro/13/13 Pro/14 | `iphone-12-portrait.png` | 1170×2532 | @3x | 6.1英寸全面屏 |
| iPhone 12/13/14 Pro Max/Plus | `iphone-12-pro-max-portrait.png` | 1284×2778 | @3x | 6.7英寸全面屏 |
| iPhone 14 Pro | `iphone-14-pro-portrait.png` | 1179×2556 | @3x | 6.1英寸动态岛 |
| iPhone 14 Pro Max | `iphone-14-pro-max-portrait.png` | 1290×2796 | @3x | 6.7英寸动态岛 |
| iPhone 15/15 Pro | `iphone-15-portrait.png` | 1179×2556 | @3x | 6.1英寸动态岛 |
| iPhone 15 Plus/Pro Max | `iphone-15-plus-portrait.png` | 1290×2796 | @3x | 6.7英寸动态岛 |

### 📱 iPhone 设备 - 横屏 (Landscape)

| 设备型号 | 文件名 | 尺寸 (px) | 像素密度 | 备注 |
|---------|--------|-----------|----------|------|
| iPhone SE (1st gen) | `iphone-se-landscape.png` | 1136×640 | @2x | 4英寸屏幕 |
| iPhone 6/7/8 | `iphone-6-landscape.png` | 1334×750 | @2x | 4.7英寸屏幕 |
| iPhone 6/7/8 Plus | `iphone-6-plus-landscape.png` | 2208×1242 | @3x | 5.5英寸屏幕 |
| iPhone X/XS/11 Pro | `iphone-x-landscape.png` | 2436×1125 | @3x | 5.8英寸全面屏 |
| iPhone XR/11 | `iphone-xr-landscape.png` | 1792×828 | @2x | 6.1英寸全面屏 |
| iPhone XS Max/11 Pro Max | `iphone-xs-max-landscape.png` | 2688×1242 | @3x | 6.5英寸全面屏 |
| iPhone 12 mini | `iphone-12-mini-landscape.png` | 2340×1080 | @3x | 5.4英寸全面屏 |
| iPhone 12/12 Pro/13/13 Pro/14 | `iphone-12-landscape.png` | 2532×1170 | @3x | 6.1英寸全面屏 |
| iPhone 12/13/14 Pro Max/Plus | `iphone-12-pro-max-landscape.png` | 2778×1284 | @3x | 6.7英寸全面屏 |
| iPhone 14 Pro | `iphone-14-pro-landscape.png` | 2556×1179 | @3x | 6.1英寸动态岛 |
| iPhone 14 Pro Max | `iphone-14-pro-max-landscape.png` | 2796×1290 | @3x | 6.7英寸动态岛 |
| iPhone 15/15 Pro | `iphone-15-landscape.png` | 2556×1179 | @3x | 6.1英寸动态岛 |
| iPhone 15 Plus/Pro Max | `iphone-15-plus-landscape.png` | 2796×1290 | @3x | 6.7英寸动态岛 |

### 📟 iPad 设备

| 设备型号 | 文件名 | 尺寸 (px) | 像素密度 | 备注 |
|---------|--------|-----------|----------|------|
| iPad (9.7") - 竖屏 | `ipad-portrait.png` | 1536×2048 | @2x | 标准iPad |
| iPad (9.7") - 横屏 | `ipad-landscape.png` | 2048×1536 | @2x | 标准iPad |
| iPad Pro (10.5") - 竖屏 | `ipad-pro-10-portrait.png` | 1668×2224 | @2x | 10.5英寸Pro |
| iPad Pro (10.5") - 横屏 | `ipad-pro-10-landscape.png` | 2224×1668 | @2x | 10.5英寸Pro |
| iPad Pro (11") - 竖屏 | `ipad-pro-11-portrait.png` | 1668×2388 | @2x | 11英寸Pro |
| iPad Pro (11") - 横屏 | `ipad-pro-11-landscape.png` | 2388×1668 | @2x | 11英寸Pro |
| iPad Pro (12.9") - 竖屏 | `ipad-pro-12-portrait.png` | 2048×2732 | @2x | 12.9英寸Pro |
| iPad Pro (12.9") - 横屏 | `ipad-pro-12-landscape.png` | 2732×2048 | @2x | 12.9英寸Pro |

### 🔄 备用图片

| 用途 | 文件名 | 尺寸 (px) | 备注 |
|------|--------|-----------|------|
| 通用备用启动屏幕 | `default.png` | 1024×1024 | 当特定设备图片缺失时使用 |

## 📂 文件结构

请将所有启动屏幕图片放置在以下目录结构中：

```
public/
└── splash/
    ├── iphone-se-portrait.png
    ├── iphone-se-landscape.png
    ├── iphone-6-portrait.png
    ├── iphone-6-landscape.png
    ├── iphone-6-plus-portrait.png
    ├── iphone-6-plus-landscape.png
    ├── iphone-x-portrait.png
    ├── iphone-x-landscape.png
    ├── iphone-xr-portrait.png
    ├── iphone-xr-landscape.png
    ├── iphone-xs-max-portrait.png
    ├── iphone-xs-max-landscape.png
    ├── iphone-12-mini-portrait.png
    ├── iphone-12-mini-landscape.png
    ├── iphone-12-portrait.png
    ├── iphone-12-landscape.png
    ├── iphone-12-pro-max-portrait.png
    ├── iphone-12-pro-max-landscape.png
    ├── iphone-14-pro-portrait.png
    ├── iphone-14-pro-landscape.png
    ├── iphone-14-pro-max-portrait.png
    ├── iphone-14-pro-max-landscape.png
    ├── iphone-15-portrait.png
    ├── iphone-15-landscape.png
    ├── iphone-15-plus-portrait.png
    ├── iphone-15-plus-landscape.png
    ├── ipad-portrait.png
    ├── ipad-landscape.png
    ├── ipad-pro-10-portrait.png
    ├── ipad-pro-10-landscape.png
    ├── ipad-pro-11-portrait.png
    ├── ipad-pro-11-landscape.png
    ├── ipad-pro-12-portrait.png
    ├── ipad-pro-12-landscape.png
    └── default.png
```

## 🎨 设计指南

### 基本要求
- **格式**: PNG格式，支持透明度
- **色彩**: RGB色彩空间
- **压缩**: 适度压缩，平衡文件大小和质量

### 设计建议

#### 1. 背景设计
- 使用应用主题色 `#4CAF50` 或白色 `#ffffff`
- 可使用渐变背景增加视觉层次
- 避免过于复杂的背景图案

#### 2. Logo放置
- Logo应居中放置
- 建议Logo宽度为屏幕宽度的30-40%
- 确保Logo在所有设备上都清晰可见

#### 3. 文字元素
- 应用名称"随心听"可放置在Logo下方
- 使用简洁、易读的字体
- 字体大小应适配不同屏幕尺寸

#### 4. 安全区域
- 为刘海屏和圆角屏幕预留安全区域
- 重要内容应距离屏幕边缘至少44px
- 考虑状态栏和Home指示器的位置

#### 5. 加载指示
- 可添加简单的加载动画或进度条
- 避免过于复杂的动画效果
- 确保加载指示与整体设计协调

### 色彩方案建议

#### 浅色主题
- 背景色: `#ffffff` 或 `#f5f5f5`
- 主色调: `#4CAF50`
- 文字色: `#333333`

#### 深色主题
- 背景色: `#1a1a1a` 或 `#2E7D32`
- 主色调: `#4CAF50`
- 文字色: `#ffffff`

## 🛠️ 制作工具推荐

### 设计软件
- **Adobe Photoshop** - 专业图像编辑
- **Sketch** - UI设计专用工具
- **Figma** - 在线协作设计工具
- **Adobe XD** - UI/UX设计工具

### 在线工具
- **PWA Asset Generator** - 自动生成PWA资源
- **App Icon Generator** - 批量生成不同尺寸图标
- **Splash Screen Generator** - 启动屏幕生成工具

### 批量处理
- **ImageMagick** - 命令行图像处理
- **Sharp** - Node.js图像处理库
- **Photoshop Actions** - 批量处理脚本

## ✅ 检查清单

在完成启动屏幕制作后，请确认以下事项：

- [ ] 所有必需的尺寸都已制作完成
- [ ] 文件命名与配置文件中的路径一致
- [ ] 图片格式为PNG
- [ ] 图片质量清晰，无模糊或失真
- [ ] 设计在不同尺寸下保持一致性
- [ ] 重要内容未被安全区域遮挡
- [ ] 文件大小适中，不影响加载速度
- [ ] 在实际设备上测试显示效果

## 🚀 部署验证

完成启动屏幕制作后，建议进行以下验证：

1. **本地测试**: 在开发环境中预览效果
2. **设备测试**: 在真实iOS设备上测试
3. **多设备验证**: 在不同型号设备上验证
4. **方向测试**: 测试竖屏和横屏显示
5. **性能检查**: 确认加载速度符合预期

---

## 📞 技术支持

如果在制作启动屏幕过程中遇到问题，可以参考：

- [Apple官方PWA文档](https://developer.apple.com/documentation/webkit/safari_web_extensions)
- [MDN PWA指南](https://developer.mozilla.org/en-US/docs/Web/Progressive_web_apps)
- [Web App Manifest规范](https://www.w3.org/TR/appmanifest/)

通过按照本清单制作启动屏幕，您的PWA应用将在所有iOS设备上提供完美的启动体验！
