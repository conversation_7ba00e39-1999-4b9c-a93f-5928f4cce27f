#!/usr/bin/env node

/**
 * 启动屏幕生成脚本
 * 用于批量生成iOS PWA所需的所有启动屏幕图片
 * 
 * 使用方法:
 * 1. 安装依赖: npm install sharp
 * 2. 准备源图片: 将logo放在 assets/logo.png (建议1024x1024px)
 * 3. 运行脚本: node generate-splash-screens.js
 */

const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

// 配置
const CONFIG = {
  // 源Logo文件路径
  logoPath: './assets/logo.png',
  // 输出目录
  outputDir: './public/splash',
  // 背景色 (浅色主题)
  backgroundColor: '#ffffff',
  // 深色主题背景色
  darkBackgroundColor: '#1a1a1a',
  // 主题色
  themeColor: '#4CAF50',
  // Logo相对屏幕的大小比例
  logoScale: 0.3,
  // 应用名称
  appName: '随心听',
  // 字体大小相对屏幕宽度的比例
  fontScale: 0.08
};

// 启动屏幕尺寸配置
const SPLASH_SIZES = [
  // iPhone 竖屏
  { name: 'iphone-se-portrait', width: 640, height: 1136, orientation: 'portrait' },
  { name: 'iphone-6-portrait', width: 750, height: 1334, orientation: 'portrait' },
  { name: 'iphone-6-plus-portrait', width: 1242, height: 2208, orientation: 'portrait' },
  { name: 'iphone-x-portrait', width: 1125, height: 2436, orientation: 'portrait' },
  { name: 'iphone-xr-portrait', width: 828, height: 1792, orientation: 'portrait' },
  { name: 'iphone-xs-max-portrait', width: 1242, height: 2688, orientation: 'portrait' },
  { name: 'iphone-12-mini-portrait', width: 1080, height: 2340, orientation: 'portrait' },
  { name: 'iphone-12-portrait', width: 1170, height: 2532, orientation: 'portrait' },
  { name: 'iphone-12-pro-max-portrait', width: 1284, height: 2778, orientation: 'portrait' },
  { name: 'iphone-14-pro-portrait', width: 1179, height: 2556, orientation: 'portrait' },
  { name: 'iphone-14-pro-max-portrait', width: 1290, height: 2796, orientation: 'portrait' },
  { name: 'iphone-15-portrait', width: 1179, height: 2556, orientation: 'portrait' },
  { name: 'iphone-15-plus-portrait', width: 1290, height: 2796, orientation: 'portrait' },
  
  // iPhone 横屏
  { name: 'iphone-se-landscape', width: 1136, height: 640, orientation: 'landscape' },
  { name: 'iphone-6-landscape', width: 1334, height: 750, orientation: 'landscape' },
  { name: 'iphone-6-plus-landscape', width: 2208, height: 1242, orientation: 'landscape' },
  { name: 'iphone-x-landscape', width: 2436, height: 1125, orientation: 'landscape' },
  { name: 'iphone-xr-landscape', width: 1792, height: 828, orientation: 'landscape' },
  { name: 'iphone-xs-max-landscape', width: 2688, height: 1242, orientation: 'landscape' },
  { name: 'iphone-12-mini-landscape', width: 2340, height: 1080, orientation: 'landscape' },
  { name: 'iphone-12-landscape', width: 2532, height: 1170, orientation: 'landscape' },
  { name: 'iphone-12-pro-max-landscape', width: 2778, height: 1284, orientation: 'landscape' },
  { name: 'iphone-14-pro-landscape', width: 2556, height: 1179, orientation: 'landscape' },
  { name: 'iphone-14-pro-max-landscape', width: 2796, height: 1290, orientation: 'landscape' },
  { name: 'iphone-15-landscape', width: 2556, height: 1179, orientation: 'landscape' },
  { name: 'iphone-15-plus-landscape', width: 2796, height: 1290, orientation: 'landscape' },
  
  // iPad
  { name: 'ipad-portrait', width: 1536, height: 2048, orientation: 'portrait' },
  { name: 'ipad-landscape', width: 2048, height: 1536, orientation: 'landscape' },
  { name: 'ipad-pro-10-portrait', width: 1668, height: 2224, orientation: 'portrait' },
  { name: 'ipad-pro-10-landscape', width: 2224, height: 1668, orientation: 'landscape' },
  { name: 'ipad-pro-11-portrait', width: 1668, height: 2388, orientation: 'portrait' },
  { name: 'ipad-pro-11-landscape', width: 2388, height: 1668, orientation: 'landscape' },
  { name: 'ipad-pro-12-portrait', width: 2048, height: 2732, orientation: 'portrait' },
  { name: 'ipad-pro-12-landscape', width: 2732, height: 2048, orientation: 'landscape' },
  
  // 默认
  { name: 'default', width: 1024, height: 1024, orientation: 'square' }
];

/**
 * 创建输出目录
 */
function ensureOutputDir() {
  if (!fs.existsSync(CONFIG.outputDir)) {
    fs.mkdirSync(CONFIG.outputDir, { recursive: true });
    console.log(`✅ 创建输出目录: ${CONFIG.outputDir}`);
  }
}

/**
 * 检查源文件是否存在
 */
function checkSourceFile() {
  if (!fs.existsSync(CONFIG.logoPath)) {
    console.error(`❌ 源Logo文件不存在: ${CONFIG.logoPath}`);
    console.log(`请将Logo文件放置在: ${CONFIG.logoPath}`);
    console.log(`建议Logo尺寸: 1024x1024px, PNG格式`);
    process.exit(1);
  }
  console.log(`✅ 找到源Logo文件: ${CONFIG.logoPath}`);
}

/**
 * 生成单个启动屏幕
 */
async function generateSplashScreen(config) {
  const { name, width, height, orientation } = config;
  const outputPath = path.join(CONFIG.outputDir, `${name}.png`);
  
  try {
    // 计算Logo尺寸
    const logoSize = Math.min(width, height) * CONFIG.logoScale;
    
    // 计算Logo位置 (居中，稍微偏上)
    const logoX = (width - logoSize) / 2;
    const logoY = orientation === 'landscape' 
      ? (height - logoSize) / 2 
      : (height - logoSize) / 2 - height * 0.05; // 竖屏时稍微偏上
    
    // 创建背景
    const background = sharp({
      create: {
        width,
        height,
        channels: 3,
        background: CONFIG.backgroundColor
      }
    });
    
    // 处理Logo
    const logo = await sharp(CONFIG.logoPath)
      .resize(Math.round(logoSize), Math.round(logoSize), {
        fit: 'contain',
        background: { r: 0, g: 0, b: 0, alpha: 0 }
      })
      .png();
    
    // 合成图像
    await background
      .composite([
        {
          input: await logo.toBuffer(),
          left: Math.round(logoX),
          top: Math.round(logoY)
        }
      ])
      .png({ quality: 90 })
      .toFile(outputPath);
    
    console.log(`✅ 生成: ${name}.png (${width}×${height})`);
    
  } catch (error) {
    console.error(`❌ 生成失败: ${name}.png`, error.message);
  }
}

/**
 * 生成深色主题启动屏幕
 */
async function generateDarkSplashScreens() {
  console.log('\n🌙 生成深色主题启动屏幕...');
  
  const darkOutputDir = path.join(CONFIG.outputDir, 'dark');
  if (!fs.existsSync(darkOutputDir)) {
    fs.mkdirSync(darkOutputDir, { recursive: true });
  }
  
  // 临时修改背景色
  const originalBg = CONFIG.backgroundColor;
  CONFIG.backgroundColor = CONFIG.darkBackgroundColor;
  
  // 生成几个主要尺寸的深色版本
  const mainSizes = SPLASH_SIZES.filter(size => 
    size.name.includes('iphone-12-portrait') || 
    size.name.includes('iphone-12-landscape') ||
    size.name.includes('ipad-portrait') ||
    size.name === 'default'
  );
  
  for (const size of mainSizes) {
    const darkConfig = {
      ...size,
      name: `dark/${size.name}`
    };
    await generateSplashScreen(darkConfig);
  }
  
  // 恢复原始背景色
  CONFIG.backgroundColor = originalBg;
}

/**
 * 生成HTML预览文件
 */
function generatePreviewHTML() {
  const previewPath = path.join(CONFIG.outputDir, 'preview.html');
  
  let html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>启动屏幕预览 - ${CONFIG.appName}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 40px;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
        }
        .item {
            background: white;
            border-radius: 12px;
            padding: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .item img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }
        .item h3 {
            margin: 15px 0 5px;
            font-size: 14px;
            color: #333;
        }
        .item p {
            margin: 0;
            font-size: 12px;
            color: #666;
        }
        .stats {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 ${CONFIG.appName} - 启动屏幕预览</h1>
        
        <div class="stats">
            <h2>📊 生成统计</h2>
            <p>总计生成: <strong>${SPLASH_SIZES.length}</strong> 个启动屏幕</p>
            <p>支持设备: iPhone SE ~ iPhone 15 系列, iPad 全系列</p>
            <p>生成时间: ${new Date().toLocaleString('zh-CN')}</p>
        </div>
        
        <div class="grid">
`;

  SPLASH_SIZES.forEach(size => {
    html += `
            <div class="item">
                <img src="${size.name}.png" alt="${size.name}" loading="lazy">
                <h3>${size.name}</h3>
                <p>${size.width}×${size.height}</p>
            </div>
`;
  });

  html += `
        </div>
    </div>
</body>
</html>
`;

  fs.writeFileSync(previewPath, html);
  console.log(`✅ 生成预览文件: ${previewPath}`);
}

/**
 * 主函数
 */
async function main() {
  console.log('🎨 启动屏幕生成器');
  console.log('==================');
  
  // 检查依赖
  try {
    require('sharp');
  } catch (error) {
    console.error('❌ 缺少依赖: sharp');
    console.log('请运行: npm install sharp');
    process.exit(1);
  }
  
  // 检查源文件
  checkSourceFile();
  
  // 创建输出目录
  ensureOutputDir();
  
  console.log(`\n🚀 开始生成 ${SPLASH_SIZES.length} 个启动屏幕...`);
  console.log(`📁 输出目录: ${CONFIG.outputDir}`);
  console.log(`🎨 背景色: ${CONFIG.backgroundColor}`);
  console.log(`📏 Logo比例: ${CONFIG.logoScale * 100}%\n`);
  
  // 生成启动屏幕
  let successCount = 0;
  for (const size of SPLASH_SIZES) {
    await generateSplashScreen(size);
    successCount++;
  }
  
  // 生成深色主题版本
  await generateDarkSplashScreens();
  
  // 生成预览文件
  generatePreviewHTML();
  
  console.log('\n🎉 生成完成!');
  console.log(`✅ 成功生成: ${successCount}/${SPLASH_SIZES.length} 个启动屏幕`);
  console.log(`📁 文件位置: ${CONFIG.outputDir}`);
  console.log(`👀 预览文件: ${CONFIG.outputDir}/preview.html`);
  console.log('\n📱 下一步:');
  console.log('1. 检查生成的启动屏幕是否符合预期');
  console.log('2. 根据需要调整设计和配置');
  console.log('3. 将文件部署到服务器');
  console.log('4. 在iOS设备上测试PWA效果');
}

// 运行主函数
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  generateSplashScreen,
  SPLASH_SIZES,
  CONFIG
};
