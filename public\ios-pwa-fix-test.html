<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    
    <!-- PWA 配置 -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="随心听测试">
    <meta name="mobile-web-app-capable" content="yes">
    
    <title>iOS PWA 修复测试</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            overflow: hidden;
            height: 100vh;
            height: 100dvh;
            position: fixed;
            width: 100%;
        }
        
        .container {
            height: calc(100vh - 60px);
            height: calc(100dvh - 60px);
            height: calc(var(--vh, 1vh) * 100 - 60px);
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .subtitle {
            font-size: 16px;
            opacity: 0.8;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .status-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 15px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        
        .status-title {
            font-size: 14px;
            opacity: 0.8;
            margin-bottom: 8px;
        }
        
        .status-value {
            font-size: 18px;
            font-weight: bold;
        }
        
        .status-value.success {
            color: #4ade80;
        }
        
        .status-value.error {
            color: #f87171;
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }
        
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .test-item:last-child {
            border-bottom: none;
        }
        
        .test-label {
            font-size: 14px;
        }
        
        .test-result {
            font-weight: bold;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
        }
        
        .test-result.pass {
            background: #4ade80;
            color: #000;
        }
        
        .test-result.fail {
            background: #f87171;
            color: #fff;
        }
        
        .mock-tabbar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            display: flex;
            align-items: center;
            justify-content: space-around;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }
        
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px;
            color: #666;
            font-size: 12px;
            text-decoration: none;
        }
        
        .tab-item.active {
            color: #007AFF;
        }
        
        .tab-icon {
            font-size: 24px;
            margin-bottom: 4px;
        }
        
        .pwa-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4ade80;
            color: #000;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            display: none;
        }
        
        /* PWA模式显示指示器 */
        @media (display-mode: standalone) {
            .pwa-indicator {
                display: block;
            }
            
            body {
                background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
            }
        }
        
        /* iPad 特殊处理 */
        @media screen and (min-width: 768px) and (max-width: 1024px) {
            .container {
                height: calc(100vh - 60px);
                height: calc(100dvh - 60px);
                height: calc(var(--vh, 1vh) * 100 - 60px);
            }
            
            .mock-tabbar {
                display: flex !important;
                height: 60px !important;
            }
        }
        
        /* 深色模式 */
        @media (prefers-color-scheme: dark) {
            .mock-tabbar {
                background: rgba(0, 0, 0, 0.95);
                border-top: 1px solid rgba(255, 255, 255, 0.1);
            }
            
            .tab-item {
                color: #ccc;
            }
            
            .tab-item.active {
                color: #007AFF;
            }
        }
    </style>
</head>
<body>
    <div class="pwa-indicator">
        🚀 PWA 模式
    </div>
    
    <div class="container">
        <div class="header">
            <div class="title">🎵 随心听 PWA 修复测试</div>
            <div class="subtitle">iOS Safari 地址栏隐藏 + iPad 底部导航修复</div>
        </div>
        
        <div class="status-grid">
            <div class="status-card">
                <div class="status-title">设备类型</div>
                <div class="status-value" id="deviceType">检测中...</div>
            </div>
            <div class="status-card">
                <div class="status-title">PWA 状态</div>
                <div class="status-value" id="pwaStatus">检测中...</div>
            </div>
            <div class="status-card">
                <div class="status-title">视口高度</div>
                <div class="status-value" id="viewportHeight">检测中...</div>
            </div>
            <div class="status-card">
                <div class="status-title">底部导航</div>
                <div class="status-value" id="tabbarStatus">检测中...</div>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">🔧 修复项目检测</div>
            <div class="test-item">
                <div class="test-label">地址栏隐藏</div>
                <div class="test-result" id="addressBarTest">检测中</div>
            </div>
            <div class="test-item">
                <div class="test-label">iPad 底部导航显示</div>
                <div class="test-result" id="ipadTabbarTest">检测中</div>
            </div>
            <div class="test-item">
                <div class="test-label">页面滚动性能</div>
                <div class="test-result" id="scrollTest">检测中</div>
            </div>
            <div class="test-item">
                <div class="test-label">双击缩放禁用</div>
                <div class="test-result" id="zoomTest">检测中</div>
            </div>
            <div class="test-item">
                <div class="test-label">安全区域适配</div>
                <div class="test-result" id="safeAreaTest">检测中</div>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">📊 详细信息</div>
            <div class="test-item">
                <div class="test-label">屏幕尺寸</div>
                <div class="test-result" id="screenSize">-</div>
            </div>
            <div class="test-item">
                <div class="test-label">视口尺寸</div>
                <div class="test-result" id="windowSize">-</div>
            </div>
            <div class="test-item">
                <div class="test-label">设备像素比</div>
                <div class="test-result" id="devicePixelRatio">-</div>
            </div>
            <div class="test-item">
                <div class="test-label">用户代理</div>
                <div class="test-result" id="userAgent" style="font-size: 10px; max-width: 200px; word-break: break-all;">-</div>
            </div>
        </div>
    </div>
    
    <!-- 模拟底部导航栏 -->
    <div class="mock-tabbar">
        <a href="#" class="tab-item active">
            <div class="tab-icon">🏠</div>
            <div>首页</div>
        </a>
        <a href="#" class="tab-item">
            <div class="tab-icon">🎵</div>
            <div>音乐</div>
        </a>
        <a href="#" class="tab-item">
            <div class="tab-icon">❤️</div>
            <div>收藏</div>
        </a>
        <a href="#" class="tab-item">
            <div class="tab-icon">👤</div>
            <div>我的</div>
        </a>
    </div>
    
    <script>
        // 设备检测
        function detectDevice() {
            const userAgent = navigator.userAgent;
            const isIOS = /iPad|iPhone|iPod/.test(userAgent);
            const isIPad = /iPad/.test(userAgent) || 
                           (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
            const isIPhone = /iPhone/.test(userAgent);
            const isAndroid = /Android/.test(userAgent);
            const isSafari = /^((?!chrome|android).)*safari/i.test(userAgent);
            const isPWA = window.matchMedia('(display-mode: standalone)').matches ||
                          window.navigator.standalone === true;
            
            return {
                isIOS,
                isIPad,
                isIPhone,
                isAndroid,
                isSafari,
                isPWA,
                isIOSSafari: isIOS && isSafari
            };
        }
        
        // 设置视口高度变量
        function setViewportHeight() {
            const vh = window.innerHeight * 0.01;
            document.documentElement.style.setProperty('--vh', `${vh}px`);
        }
        
        // 运行测试
        function runTests() {
            const device = detectDevice();
            
            // 设备类型
            let deviceTypeText = '';
            if (device.isIPad) deviceTypeText = 'iPad';
            else if (device.isIPhone) deviceTypeText = 'iPhone';
            else if (device.isAndroid) deviceTypeText = 'Android';
            else deviceTypeText = '桌面';
            
            document.getElementById('deviceType').textContent = deviceTypeText;
            document.getElementById('deviceType').className = 'status-value success';
            
            // PWA 状态
            const pwaStatusEl = document.getElementById('pwaStatus');
            if (device.isPWA) {
                pwaStatusEl.textContent = 'PWA 模式';
                pwaStatusEl.className = 'status-value success';
            } else {
                pwaStatusEl.textContent = '浏览器模式';
                pwaStatusEl.className = 'status-value error';
            }
            
            // 视口高度
            document.getElementById('viewportHeight').textContent = window.innerHeight + 'px';
            document.getElementById('viewportHeight').className = 'status-value success';
            
            // 底部导航状态
            const tabbar = document.querySelector('.mock-tabbar');
            const tabbarVisible = window.getComputedStyle(tabbar).display !== 'none';
            const tabbarStatusEl = document.getElementById('tabbarStatus');
            
            if (device.isIPad && tabbarVisible) {
                tabbarStatusEl.textContent = '正常显示';
                tabbarStatusEl.className = 'status-value success';
            } else if (device.isIPad && !tabbarVisible) {
                tabbarStatusEl.textContent = '未显示';
                tabbarStatusEl.className = 'status-value error';
            } else {
                tabbarStatusEl.textContent = '不适用';
                tabbarStatusEl.className = 'status-value';
            }
            
            // 测试项目
            // 地址栏隐藏测试
            const addressBarTestEl = document.getElementById('addressBarTest');
            if (device.isPWA || (device.isIOSSafari && window.innerHeight > 600)) {
                addressBarTestEl.textContent = 'PASS';
                addressBarTestEl.className = 'test-result pass';
            } else {
                addressBarTestEl.textContent = 'FAIL';
                addressBarTestEl.className = 'test-result fail';
            }
            
            // iPad 底部导航测试
            const ipadTabbarTestEl = document.getElementById('ipadTabbarTest');
            if (!device.isIPad) {
                ipadTabbarTestEl.textContent = 'N/A';
                ipadTabbarTestEl.className = 'test-result';
            } else if (tabbarVisible) {
                ipadTabbarTestEl.textContent = 'PASS';
                ipadTabbarTestEl.className = 'test-result pass';
            } else {
                ipadTabbarTestEl.textContent = 'FAIL';
                ipadTabbarTestEl.className = 'test-result fail';
            }
            
            // 滚动性能测试
            const scrollTestEl = document.getElementById('scrollTest');
            const hasWebkitScrolling = getComputedStyle(document.body)['-webkit-overflow-scrolling'] === 'touch';
            if (hasWebkitScrolling || !device.isIOS) {
                scrollTestEl.textContent = 'PASS';
                scrollTestEl.className = 'test-result pass';
            } else {
                scrollTestEl.textContent = 'FAIL';
                scrollTestEl.className = 'test-result fail';
            }
            
            // 双击缩放测试
            const zoomTestEl = document.getElementById('zoomTest');
            const touchAction = getComputedStyle(document.body)['touch-action'];
            if (touchAction === 'manipulation') {
                zoomTestEl.textContent = 'PASS';
                zoomTestEl.className = 'test-result pass';
            } else {
                zoomTestEl.textContent = 'FAIL';
                zoomTestEl.className = 'test-result fail';
            }
            
            // 安全区域测试
            const safeAreaTestEl = document.getElementById('safeAreaTest');
            const hasEnvSupport = CSS.supports('padding', 'env(safe-area-inset-bottom)');
            if (hasEnvSupport) {
                safeAreaTestEl.textContent = 'PASS';
                safeAreaTestEl.className = 'test-result pass';
            } else {
                safeAreaTestEl.textContent = 'FAIL';
                safeAreaTestEl.className = 'test-result fail';
            }
            
            // 详细信息
            document.getElementById('screenSize').textContent = `${screen.width}×${screen.height}`;
            document.getElementById('windowSize').textContent = `${window.innerWidth}×${window.innerHeight}`;
            document.getElementById('devicePixelRatio').textContent = window.devicePixelRatio;
            document.getElementById('userAgent').textContent = navigator.userAgent.substring(0, 50) + '...';
        }
        
        // 初始化
        setViewportHeight();
        runTests();
        
        // 监听变化
        window.addEventListener('resize', () => {
            setViewportHeight();
            setTimeout(runTests, 100);
        });
        
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                setViewportHeight();
                runTests();
            }, 500);
        });
        
        if (window.visualViewport) {
            window.visualViewport.addEventListener('resize', () => {
                setViewportHeight();
                setTimeout(runTests, 100);
            });
        }
        
        console.log('🧪 iOS PWA 修复测试页面已加载');
        console.log('设备信息:', detectDevice());
    </script>
</body>
</html>
