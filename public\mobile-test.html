<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover, shrink-to-fit=no">
    <title>移动端地址栏隐藏测试</title>
    
    <!-- iOS Safari 专用配置 -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="移动端测试">
    <meta name="apple-touch-fullscreen" content="yes">
    <meta name="format-detection" content="telephone=no, date=no, address=no, email=no">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-user-select: none;
            -webkit-touch-callout: none;
            -webkit-tap-highlight-color: transparent;
        }
        
        html, body {
            height: 100vh;
            height: 100dvh;
            width: 100vw;
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            -webkit-overflow-scrolling: auto;
            overscroll-behavior: none;
        }
        
        .container {
            height: calc(100vh - 60px);
            height: calc(100dvh - 60px);
            width: 100vw;
            display: flex;
            flex-direction: column;
            padding: 20px;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .subtitle {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .info-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 15px;
            backdrop-filter: blur(10px);
        }
        
        .info-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .info-item:last-child {
            margin-bottom: 0;
        }
        
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #4CAF50;
        }
        
        .status-indicator.error {
            background: #f44336;
        }
        
        .status-indicator.warning {
            background: #ff9800;
        }
        
        .buttons {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .button {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 20px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .button:active {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(0.98);
        }
        
        /* 模拟移动端tabbar */
        .mock-tabbar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(20px);
            display: flex;
            align-items: center;
            justify-content: space-around;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            z-index: 1000;
        }
        
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px;
            color: rgba(255, 255, 255, 0.7);
            font-size: 12px;
        }
        
        .tab-item.active {
            color: #4CAF50;
        }
        
        .tab-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .log {
            background: rgba(0, 0, 0, 0.8);
            border-radius: 8px;
            padding: 10px;
            font-size: 11px;
            font-family: monospace;
            max-height: 120px;
            overflow-y: auto;
            margin-bottom: 20px;
        }
        
        .log-entry {
            margin-bottom: 3px;
            opacity: 0.9;
        }
        
        /* 地址栏隐藏时的样式 */
        .address-bar-hidden .container {
            height: calc(100vh - 56px) !important;
            height: calc(100dvh - 56px) !important;
        }
        
        .address-bar-hidden .mock-tabbar {
            height: 56px !important;
        }
        
        /* iOS Safari 专用样式 */
        @supports (-webkit-touch-callout: none) {
            html, body {
                position: fixed !important;
                overflow: hidden !important;
                height: 100% !important;
                width: 100% !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">📱 移动端测试</div>
            <div class="subtitle">地址栏隐藏 + Tabbar 适配</div>
        </div>
        
        <div class="info-section">
            <div class="info-title">📊 设备信息</div>
            <div class="info-item">
                <span>屏幕尺寸</span>
                <span id="screenSize">-</span>
            </div>
            <div class="info-item">
                <span>视口尺寸</span>
                <span id="viewportSize">-</span>
            </div>
            <div class="info-item">
                <span>容器高度</span>
                <span id="containerHeight">-</span>
            </div>
            <div class="info-item">
                <span>Tabbar高度</span>
                <span id="tabbarHeight">-</span>
            </div>
        </div>
        
        <div class="info-section">
            <div class="info-title">🔍 状态检测</div>
            <div class="info-item">
                <span>iOS设备</span>
                <div class="status-indicator" id="iosStatus"></div>
            </div>
            <div class="info-item">
                <span>Safari浏览器</span>
                <div class="status-indicator" id="safariStatus"></div>
            </div>
            <div class="info-item">
                <span>PWA模式</span>
                <div class="status-indicator" id="pwaStatus"></div>
            </div>
            <div class="info-item">
                <span>地址栏隐藏</span>
                <div class="status-indicator" id="addressBarStatus"></div>
            </div>
        </div>
        
        <div class="buttons">
            <button class="button" onclick="hideAddressBar()">🔄 隐藏地址栏</button>
            <button class="button" onclick="toggleTabbarMode()">📱 切换Tabbar模式</button>
            <button class="button" onclick="testFullscreen()">🖥️ 测试全屏</button>
            <button class="button" onclick="clearLog()">🗑️ 清除日志</button>
        </div>
        
        <div class="log" id="log"></div>
    </div>
    
    <!-- 模拟移动端tabbar -->
    <div class="mock-tabbar" id="tabbar">
        <div class="tab-item active">
            <div class="tab-icon">🏠</div>
            <div>首页</div>
        </div>
        <div class="tab-item">
            <div class="tab-icon">🎵</div>
            <div>音乐</div>
        </div>
        <div class="tab-item">
            <div class="tab-icon">❤️</div>
            <div>收藏</div>
        </div>
        <div class="tab-item">
            <div class="tab-icon">👤</div>
            <div>我的</div>
        </div>
    </div>
    
    <script>
        let isAddressBarHidden = false;
        
        // 日志功能
        function log(message) {
            const logElement = document.getElementById('log');
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            logElement.appendChild(entry);
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        // 设备检测
        function detectDevice() {
            const userAgent = navigator.userAgent;
            const isIOS = /iPad|iPhone|iPod/.test(userAgent);
            const isSafari = /^((?!chrome|android).)*safari/i.test(userAgent);
            const isPWA = window.matchMedia('(display-mode: standalone)').matches || 
                          window.navigator.standalone === true;
            
            document.getElementById('iosStatus').className = 
                'status-indicator ' + (isIOS ? '' : 'error');
            document.getElementById('safariStatus').className = 
                'status-indicator ' + (isSafari ? '' : 'error');
            document.getElementById('pwaStatus').className = 
                'status-indicator ' + (isPWA ? '' : 'warning');
            
            log(`设备检测: iOS=${isIOS}, Safari=${isSafari}, PWA=${isPWA}`);
            
            return { isIOS, isSafari, isPWA };
        }
        
        // 更新尺寸信息
        function updateSizeInfo() {
            const container = document.querySelector('.container');
            const tabbar = document.getElementById('tabbar');
            
            document.getElementById('screenSize').textContent = 
                `${screen.width}×${screen.height}`;
            document.getElementById('viewportSize').textContent = 
                `${window.innerWidth}×${window.innerHeight}`;
            document.getElementById('containerHeight').textContent = 
                `${container.offsetHeight}px`;
            document.getElementById('tabbarHeight').textContent = 
                `${tabbar.offsetHeight}px`;
            
            // 检查地址栏状态
            const heightDiff = screen.height - window.innerHeight;
            const isHidden = heightDiff < 100;
            
            document.getElementById('addressBarStatus').className = 
                'status-indicator ' + (isHidden ? '' : 'error');
            
            log(`尺寸更新: 屏幕=${screen.height}, 视口=${window.innerHeight}, 差值=${heightDiff}`);
        }
        
        // 地址栏隐藏
        function hideAddressBar() {
            log('开始隐藏地址栏...');
            
            const device = detectDevice();
            if (device.isPWA) {
                log('PWA模式，无需隐藏地址栏');
                return;
            }
            
            // 应用地址栏隐藏样式
            document.documentElement.classList.add('address-bar-hidden');
            document.body.classList.add('address-bar-hidden');
            
            // 设置视口高度变量
            if (window.visualViewport) {
                const vh = window.visualViewport.height;
                document.documentElement.style.setProperty('--vh', vh + 'px');
                log(`设置--vh=${vh}px`);
            }
            
            // 滚动隐藏
            window.scrollTo(0, 1);
            requestAnimationFrame(() => {
                window.scrollTo(0, 1);
                setTimeout(() => {
                    window.scrollTo(0, 0);
                }, 50);
            });
            
            isAddressBarHidden = true;
            log('地址栏隐藏完成');
            
            setTimeout(updateSizeInfo, 500);
        }
        
        // 切换Tabbar模式
        function toggleTabbarMode() {
            const tabbar = document.getElementById('tabbar');
            const container = document.querySelector('.container');
            
            if (isAddressBarHidden) {
                // 恢复正常模式
                document.documentElement.classList.remove('address-bar-hidden');
                document.body.classList.remove('address-bar-hidden');
                tabbar.style.height = '60px';
                container.style.height = 'calc(100vh - 60px)';
                isAddressBarHidden = false;
                log('恢复正常Tabbar模式');
            } else {
                // 启用地址栏隐藏模式
                hideAddressBar();
            }
            
            setTimeout(updateSizeInfo, 300);
        }
        
        // 测试全屏
        function testFullscreen() {
            log('测试全屏模式...');
            
            document.documentElement.style.position = 'fixed';
            document.documentElement.style.width = '100vw';
            document.documentElement.style.height = '100vh';
            document.documentElement.style.overflow = 'hidden';
            
            document.body.style.position = 'fixed';
            document.body.style.width = '100vw';
            document.body.style.height = '100vh';
            document.body.style.overflow = 'hidden';
            
            log('已应用全屏样式');
            setTimeout(updateSizeInfo, 500);
        }
        
        // 初始化
        function init() {
            log('页面初始化...');
            detectDevice();
            updateSizeInfo();
            
            // 自动尝试隐藏地址栏
            setTimeout(() => {
                log('自动尝试隐藏地址栏...');
                hideAddressBar();
            }, 1000);
        }
        
        // 事件监听
        window.addEventListener('load', init);
        window.addEventListener('resize', updateSizeInfo);
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                log('方向变化，重新检测');
                updateSizeInfo();
                if (isAddressBarHidden) {
                    hideAddressBar();
                }
            }, 500);
        });
        
        if (window.visualViewport) {
            window.visualViewport.addEventListener('resize', updateSizeInfo);
        }
        
        // 定期更新
        setInterval(updateSizeInfo, 3000);
    </script>
</body>
</html>
