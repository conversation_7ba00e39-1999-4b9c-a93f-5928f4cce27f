/**
 * 简化的PWA优化方案
 * 参考优秀PWA网站的实现方式
 */

// 设备检测
export function getDeviceInfo() {
  const userAgent = navigator.userAgent;
  const isIOS = /iPad|iPhone|iPod/.test(userAgent);
  const isIPad = /iPad/.test(userAgent) || 
                 (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
  const isMobile = window.innerWidth < 768 || 
                   /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
  const isPWA = window.matchMedia('(display-mode: standalone)').matches ||
                (window.navigator as any).standalone === true;

  return { isIOS, isIPad, isMobile, isPWA };
}

// 设置视口高度变量
export function setViewportHeight() {
  const vh = window.innerHeight * 0.01;
  document.documentElement.style.setProperty('--vh', `${vh}px`);
  
  if (window.visualViewport) {
    const visualVh = window.visualViewport.height * 0.01;
    document.documentElement.style.setProperty('--vh', `${visualVh}px`);
  }
}

// 简化的移动端优化
export function applySimpleMobileOptimizations() {
  const device = getDeviceInfo();
  
  console.log('📱 应用简化移动端优化:', device);

  // 添加设备类型类
  if (device.isMobile) {
    document.documentElement.classList.add('mobile');
  }
  
  if (device.isIPad) {
    document.documentElement.classList.add('ipad');
  }
  
  if (device.isPWA) {
    document.documentElement.classList.add('pwa-mode');
  }

  // 注入基础优化样式
  const optimizationStyles = `
    /* 基础移动端优化 */
    .mobile {
      -webkit-text-size-adjust: 100%;
      -webkit-tap-highlight-color: transparent;
    }

    /* 禁用双击缩放 */
    .mobile * {
      touch-action: manipulation;
      -webkit-user-select: none;
      user-select: none;
    }

    /* 允许文本选择的元素 */
    .mobile input,
    .mobile textarea,
    .mobile [contenteditable] {
      -webkit-user-select: auto;
      user-select: auto;
      touch-action: auto;
    }

    /* iOS 滚动优化 */
    .mobile .main-content,
    .mobile .mobile-content,
    .mobile .n-scrollbar {
      -webkit-overflow-scrolling: touch;
      overscroll-behavior: contain;
    }

    /* iPad 确保显示底部导航 */
    .ipad.mobile .app-menu {
      display: block !important;
    }

    /* PWA 模式优化 */
    .pwa-mode {
      height: 100vh;
      height: 100dvh;
      overflow: hidden;
    }

    .pwa-mode body {
      position: fixed;
      width: 100%;
      height: 100%;
      overflow: hidden;
    }
  `;

  // 移除旧样式
  const oldStyle = document.getElementById('simple-mobile-optimization');
  if (oldStyle) oldStyle.remove();

  // 添加新样式
  const styleSheet = document.createElement('style');
  styleSheet.id = 'simple-mobile-optimization';
  styleSheet.textContent = optimizationStyles;
  document.head.appendChild(styleSheet);

  // 设置视口高度
  setViewportHeight();

  // 监听视口变化
  window.addEventListener('resize', setViewportHeight);
  window.addEventListener('orientationchange', () => {
    setTimeout(setViewportHeight, 100);
  });

  if (window.visualViewport) {
    window.visualViewport.addEventListener('resize', setViewportHeight);
  }

  console.log('✅ 简化移动端优化应用完成');
}

// 简化的地址栏隐藏
export function hideAddressBarSimple() {
  const device = getDeviceInfo();
  
  // PWA模式不需要隐藏
  if (device.isPWA) return;
  
  // 只在iOS Safari上隐藏
  if (!device.isIOS) return;

  console.log('🔧 简化地址栏隐藏');

  const hideBar = () => {
    if (window.pageYOffset === 0) {
      window.scrollTo(0, 1);
    }
    setTimeout(() => {
      window.scrollTo(0, 0);
      window.dispatchEvent(new Event('resize'));
    }, 50);
  };

  // 立即执行
  hideBar();

  // 用户交互后执行
  let hasInteracted = false;
  const handleInteraction = () => {
    if (!hasInteracted) {
      hasInteracted = true;
      hideBar();
      document.removeEventListener('touchstart', handleInteraction);
      document.removeEventListener('click', handleInteraction);
    }
  };

  document.addEventListener('touchstart', handleInteraction, { passive: true });
  document.addEventListener('click', handleInteraction);

  // 页面加载完成后执行
  window.addEventListener('load', hideBar);
}

// 防止页面弹跳
export function preventBounce() {
  // 防止双击缩放
  let lastTouchEnd = 0;
  document.addEventListener('touchend', (e) => {
    const now = Date.now();
    if (now - lastTouchEnd <= 300) {
      e.preventDefault();
    }
    lastTouchEnd = now;
  }, false);

  // 防止橡皮筋效果
  document.addEventListener('touchmove', (e) => {
    if (e.scale !== 1) {
      e.preventDefault();
    }
  }, { passive: false });
}

// 初始化简化PWA优化
export function initSimplePWA() {
  console.log('🚀 初始化简化PWA优化...');

  // 应用基础优化
  applySimpleMobileOptimizations();

  // 隐藏地址栏
  hideAddressBarSimple();

  // 防止页面弹跳
  preventBounce();

  console.log('✅ 简化PWA优化初始化完成');
}
