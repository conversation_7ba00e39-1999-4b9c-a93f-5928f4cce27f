/**
 * 简化的PWA优化方案
 * 参考优秀PWA网站的实现方式
 */

// 设备检测
export function getDeviceInfo() {
  const userAgent = navigator.userAgent;
  const isIOS = /iPad|iPhone|iPod/.test(userAgent);
  const isIPad = /iPad/.test(userAgent) || 
                 (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
  const isMobile = window.innerWidth < 768 || 
                   /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
  const isPWA = window.matchMedia('(display-mode: standalone)').matches ||
                (window.navigator as any).standalone === true;

  return { isIOS, isIPad, isMobile, isPWA };
}

// 设置视口高度变量
export function setViewportHeight() {
  const vh = window.innerHeight * 0.01;
  document.documentElement.style.setProperty('--vh', `${vh}px`);
  
  if (window.visualViewport) {
    const visualVh = window.visualViewport.height * 0.01;
    document.documentElement.style.setProperty('--vh', `${visualVh}px`);
  }
}

// 完美PWA优化 - 参考dy.521668.xyz网站
export function applyPerfectPWAOptimizations() {
  const device = getDeviceInfo();

  console.log('🎯 应用完美PWA优化 (参考dy.521668.xyz):', device);

  // 添加设备类型类
  if (device.isMobile) {
    document.documentElement.classList.add('mobile');
  }

  if (device.isIPad) {
    document.documentElement.classList.add('ipad');
  }

  if (device.isPWA) {
    document.documentElement.classList.add('pwa-mode');
  }

  // 注入完美PWA样式 - 完全参考dy.521668.xyz
  const perfectPWAStyles = `
    /* 全局PWA样式 - 参考完美网站 */
    :root {
      --safe-area-inset-bottom: env(safe-area-inset-bottom, 0px);
      --safe-area-inset-top: env(safe-area-inset-top, 0px);
      --app-height: 100vh;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      -webkit-tap-highlight-color: transparent;
    }

    html, body {
      height: 100vh;
      width: 100vw;
      margin: 0;
      padding: 0;
      overflow-x: hidden;
      font-family: 'Helvetica Neue', Arial, sans-serif;
    }

    /* 移动端基础优化 */
    .mobile {
      -webkit-text-size-adjust: 100%;
      -webkit-tap-highlight-color: transparent;
    }

    /* 禁用双击缩放 */
    .mobile * {
      touch-action: manipulation;
      -webkit-user-select: none;
      user-select: none;
    }

    /* 允许文本选择的元素 */
    .mobile input,
    .mobile textarea,
    .mobile [contenteditable] {
      -webkit-user-select: auto;
      user-select: auto;
      touch-action: auto;
    }

    /* 主容器样式 - 完全参考完美网站 */
    .mobile .main-content,
    .mobile .mobile-content {
      height: calc(100vh - 60px);
      width: 100vw;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
      scrollbar-width: none;
      position: relative;
      z-index: 1;
      box-sizing: border-box;
    }

    .mobile .main-content::-webkit-scrollbar,
    .mobile .mobile-content::-webkit-scrollbar {
      display: none;
    }

    /* PWA模式特殊样式 */
    .pwa-mode {
      overflow: hidden;
    }

    /* 确保在全屏模式下正确显示 */
    @media screen and (display-mode: standalone),
           screen and (display-mode: fullscreen) {
      body {
        height: var(--app-height);
      }

      html {
        height: var(--app-height);
      }

      .mobile .main-content,
      .mobile .mobile-content {
        height: calc(var(--app-height) - 60px - var(--safe-area-inset-bottom));
        padding-bottom: calc(70px + var(--safe-area-inset-bottom));
      }
    }
  `;

  // 移除旧样式
  const oldStyle = document.getElementById('perfect-pwa-optimization');
  if (oldStyle) oldStyle.remove();

  // 添加新样式
  const styleSheet = document.createElement('style');
  styleSheet.id = 'perfect-pwa-optimization';
  styleSheet.textContent = perfectPWAStyles;
  document.head.appendChild(styleSheet);

  // 设置视口高度变量 - 参考完美网站
  function setPerfectViewportHeight() {
    const vh = window.innerHeight * 0.01;
    document.documentElement.style.setProperty('--vh', `${vh}px`);
    document.documentElement.style.setProperty('--app-height', `${window.innerHeight}px`);

    if (window.visualViewport) {
      const visualVh = window.visualViewport.height * 0.01;
      document.documentElement.style.setProperty('--vh', `${visualVh}px`);
      document.documentElement.style.setProperty('--app-height', `${window.visualViewport.height}px`);
    }
  }

  setPerfectViewportHeight();

  // 监听视口变化
  window.addEventListener('resize', setPerfectViewportHeight);
  window.addEventListener('orientationchange', () => {
    setTimeout(setPerfectViewportHeight, 100);
  });

  if (window.visualViewport) {
    window.visualViewport.addEventListener('resize', setPerfectViewportHeight);
  }

  console.log('✅ 完美PWA优化应用完成');
}

// 简化的地址栏隐藏
export function hideAddressBarSimple() {
  const device = getDeviceInfo();
  
  // PWA模式不需要隐藏
  if (device.isPWA) return;
  
  // 只在iOS Safari上隐藏
  if (!device.isIOS) return;

  console.log('🔧 简化地址栏隐藏');

  const hideBar = () => {
    if (window.pageYOffset === 0) {
      window.scrollTo(0, 1);
    }
    setTimeout(() => {
      window.scrollTo(0, 0);
      window.dispatchEvent(new Event('resize'));
    }, 50);
  };

  // 立即执行
  hideBar();

  // 用户交互后执行
  let hasInteracted = false;
  const handleInteraction = () => {
    if (!hasInteracted) {
      hasInteracted = true;
      hideBar();
      document.removeEventListener('touchstart', handleInteraction);
      document.removeEventListener('click', handleInteraction);
    }
  };

  document.addEventListener('touchstart', handleInteraction, { passive: true });
  document.addEventListener('click', handleInteraction);

  // 页面加载完成后执行
  window.addEventListener('load', hideBar);
}

// 防止页面弹跳
export function preventBounce() {
  // 防止双击缩放
  let lastTouchEnd = 0;
  document.addEventListener('touchend', (e) => {
    const now = Date.now();
    if (now - lastTouchEnd <= 300) {
      e.preventDefault();
    }
    lastTouchEnd = now;
  }, false);

  // 防止橡皮筋效果
  document.addEventListener('touchmove', (e) => {
    if (e.scale !== 1) {
      e.preventDefault();
    }
  }, { passive: false });
}

// 初始化完美PWA优化
export function initPerfectPWA() {
  console.log('🚀 初始化完美PWA优化 (参考dy.521668.xyz)...');

  // 应用完美PWA优化
  applyPerfectPWAOptimizations();

  // 隐藏地址栏
  hideAddressBarSimple();

  // 防止页面弹跳
  preventBounce();

  console.log('✅ 完美PWA优化初始化完成');
}
