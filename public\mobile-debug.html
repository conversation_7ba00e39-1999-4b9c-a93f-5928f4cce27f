<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>移动端调试 - 随心听</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            overflow-x: hidden;
        }

        .debug-container {
            padding: 20px;
            min-height: calc(100vh - 60px);
            padding-bottom: 80px;
        }

        .debug-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .debug-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .debug-subtitle {
            font-size: 14px;
            opacity: 0.8;
        }

        .debug-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }

        .debug-section h3 {
            font-size: 18px;
            margin-bottom: 15px;
            color: #4CAF50;
        }

        .debug-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }

        .debug-item {
            background: rgba(0, 0, 0, 0.2);
            padding: 10px;
            border-radius: 8px;
        }

        .debug-label {
            font-size: 12px;
            opacity: 0.7;
            margin-bottom: 5px;
        }

        .debug-value {
            font-size: 14px;
            font-weight: bold;
            color: #4CAF50;
        }

        .test-content {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #4CAF50;
        }

        .bottom-nav {
            position: fixed;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100vw;
            height: 60px;
            padding-bottom: env(safe-area-inset-bottom, 0px);
            background: rgba(18, 18, 18, 0.98);
            z-index: 1000;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid rgba(51, 51, 51, 0.2);
            box-shadow: 0 -1px 8px rgba(0, 0, 0, 0.2);
        }

        .nav-button {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: rgba(255, 255, 255, 0.6);
            font-size: 12px;
            padding: 8px 0;
            transition: all 0.3s;
            cursor: pointer;
            width: 25%;
        }

        .nav-button.active {
            color: #4CAF50;
            transform: scale(1.05);
            font-weight: 500;
        }

        .nav-icon {
            font-size: 24px;
            margin-bottom: 4px;
        }

        .scroll-test {
            height: 200px;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            padding: 10px;
        }

        .scroll-item {
            padding: 10px;
            margin: 5px 0;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-ok {
            background: #4CAF50;
        }

        .status-warning {
            background: #FF9800;
        }

        .status-error {
            background: #F44336;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <div class="debug-header">
            <div class="debug-title">🔧 移动端调试工具</div>
            <div class="debug-subtitle">诊断随心听移动端显示问题</div>
        </div>

        <div class="debug-section">
            <h3>📱 设备信息</h3>
            <div class="debug-info">
                <div class="debug-item">
                    <div class="debug-label">设备类型</div>
                    <div class="debug-value" id="deviceType">检测中...</div>
                </div>
                <div class="debug-item">
                    <div class="debug-label">屏幕尺寸</div>
                    <div class="debug-value" id="screenSize">检测中...</div>
                </div>
                <div class="debug-item">
                    <div class="debug-label">视口尺寸</div>
                    <div class="debug-value" id="viewportSize">检测中...</div>
                </div>
                <div class="debug-item">
                    <div class="debug-label">PWA状态</div>
                    <div class="debug-value" id="pwaStatus">检测中...</div>
                </div>
            </div>
        </div>

        <div class="debug-section">
            <h3>🎯 布局检测</h3>
            <div class="test-content">
                <div><span class="status-indicator" id="contentStatus"></span>内容区域显示: <span id="contentVisible">检测中</span></div>
                <div><span class="status-indicator" id="tabbarStatus"></span>底部导航显示: <span id="tabbarVisible">检测中</span></div>
                <div><span class="status-indicator" id="scrollStatus"></span>滚动功能: <span id="scrollWorking">检测中</span></div>
                <div><span class="status-indicator" id="heightStatus"></span>高度计算: <span id="heightCalc">检测中</span></div>
            </div>
        </div>

        <div class="debug-section">
            <h3>📏 尺寸信息</h3>
            <div class="debug-info">
                <div class="debug-item">
                    <div class="debug-label">窗口高度</div>
                    <div class="debug-value" id="windowHeight">检测中...</div>
                </div>
                <div class="debug-item">
                    <div class="debug-label">文档高度</div>
                    <div class="debug-value" id="documentHeight">检测中...</div>
                </div>
                <div class="debug-item">
                    <div class="debug-label">安全区域</div>
                    <div class="debug-value" id="safeArea">检测中...</div>
                </div>
                <div class="debug-item">
                    <div class="debug-label">导航栏高度</div>
                    <div class="debug-value" id="navHeight">60px</div>
                </div>
            </div>
        </div>

        <div class="debug-section">
            <h3>🔄 滚动测试</h3>
            <div class="scroll-test" id="scrollTest">
                <div class="scroll-item">滚动项目 1</div>
                <div class="scroll-item">滚动项目 2</div>
                <div class="scroll-item">滚动项目 3</div>
                <div class="scroll-item">滚动项目 4</div>
                <div class="scroll-item">滚动项目 5</div>
                <div class="scroll-item">滚动项目 6</div>
                <div class="scroll-item">滚动项目 7</div>
                <div class="scroll-item">滚动项目 8</div>
                <div class="scroll-item">滚动项目 9</div>
                <div class="scroll-item">滚动项目 10</div>
            </div>
        </div>

        <div class="debug-section">
            <h3>🔗 测试链接</h3>
            <div class="test-content">
                <p><a href="/" style="color: #4CAF50;">返回主应用</a></p>
                <p><a href="/perfect-pwa-test.html" style="color: #4CAF50;">完美PWA测试</a></p>
                <p><a href="/tabbar-test.html" style="color: #4CAF50;">Tabbar专用测试</a></p>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="bottom-nav">
        <div class="nav-button active">
            <div class="nav-icon">🏠</div>
            <span>首页</span>
        </div>
        <div class="nav-button">
            <div class="nav-icon">🔍</div>
            <span>搜索</span>
        </div>
        <div class="nav-button">
            <div class="nav-icon">❤️</div>
            <span>收藏</span>
        </div>
        <div class="nav-button">
            <div class="nav-icon">👤</div>
            <span>我的</span>
        </div>
    </div>

    <script>
        // 设备检测
        function detectDevice() {
            const userAgent = navigator.userAgent;
            const isIOS = /iPad|iPhone|iPod/.test(userAgent);
            const isIPad = /iPad/.test(userAgent) || 
                           (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
            const isIPhone = /iPhone/.test(userAgent);
            const isPWA = window.matchMedia('(display-mode: standalone)').matches ||
                          window.navigator.standalone === true;
            
            return { isIOS, isIPad, isIPhone, isPWA };
        }

        // 运行诊断
        function runDiagnostics() {
            const device = detectDevice();

            // 设备信息
            let deviceTypeText = '';
            if (device.isIPad) deviceTypeText = 'iPad';
            else if (device.isIPhone) deviceTypeText = 'iPhone';
            else if (device.isIOS) deviceTypeText = 'iOS设备';
            else deviceTypeText = '其他设备';

            document.getElementById('deviceType').textContent = deviceTypeText;
            document.getElementById('screenSize').textContent = `${screen.width}×${screen.height}`;
            document.getElementById('viewportSize').textContent = `${window.innerWidth}×${window.innerHeight}`;
            document.getElementById('pwaStatus').textContent = device.isPWA ? 'PWA模式' : '浏览器模式';

            // 检测主应用的实际元素高度
            checkMainAppElements();
            
            // 尺寸信息
            document.getElementById('windowHeight').textContent = window.innerHeight + 'px';
            document.getElementById('documentHeight').textContent = document.documentElement.scrollHeight + 'px';
            
            // 安全区域
            const safeAreaTop = getComputedStyle(document.documentElement).getPropertyValue('--safe-area-inset-top') || '0px';
            const safeAreaBottom = getComputedStyle(document.documentElement).getPropertyValue('--safe-area-inset-bottom') || '0px';
            document.getElementById('safeArea').textContent = `顶部:${safeAreaTop} 底部:${safeAreaBottom}`;
            
            // 布局检测
            const container = document.querySelector('.debug-container');
            const bottomNav = document.querySelector('.bottom-nav');
            
            // 内容区域检测
            const contentRect = container.getBoundingClientRect();
            const isContentVisible = contentRect.top >= 0 && contentRect.top < window.innerHeight;
            document.getElementById('contentVisible').textContent = isContentVisible ? '正常' : '被遮挡';
            document.getElementById('contentStatus').className = `status-indicator ${isContentVisible ? 'status-ok' : 'status-error'}`;
            
            // 底部导航检测
            const navRect = bottomNav.getBoundingClientRect();
            const isNavVisible = navRect.bottom <= window.innerHeight;
            document.getElementById('tabbarVisible').textContent = isNavVisible ? '正常' : '超出视口';
            document.getElementById('tabbarStatus').className = `status-indicator ${isNavVisible ? 'status-ok' : 'status-warning'}`;
            
            // 滚动检测
            const scrollTest = document.getElementById('scrollTest');
            const canScroll = scrollTest.scrollHeight > scrollTest.clientHeight;
            document.getElementById('scrollWorking').textContent = canScroll ? '正常' : '无需滚动';
            document.getElementById('scrollStatus').className = `status-indicator ${canScroll ? 'status-ok' : 'status-warning'}`;
            
            // 高度计算检测
            const expectedHeight = window.innerHeight - 60; // 减去导航栏高度
            const actualHeight = container.offsetHeight;
            const heightOk = Math.abs(actualHeight - expectedHeight) < 100; // 允许100px误差
            document.getElementById('heightCalc').textContent = `期望:${expectedHeight}px 实际:${actualHeight}px`;
            document.getElementById('heightStatus').className = `status-indicator ${heightOk ? 'status-ok' : 'status-warning'}`;
            
            console.log('🔧 移动端诊断结果:', {
                device,
                contentVisible: isContentVisible,
                navVisible: isNavVisible,
                canScroll,
                heightOk,
                contentRect,
                navRect
            });
        }

        // 检测主应用的实际元素高度
        function checkMainAppElements() {
            // 尝试在新窗口中打开主应用并检测元素
            const mainAppUrl = window.location.origin + '/';

            // 创建一个隐藏的iframe来检测主应用
            const iframe = document.createElement('iframe');
            iframe.src = mainAppUrl;
            iframe.style.position = 'absolute';
            iframe.style.left = '-9999px';
            iframe.style.width = window.innerWidth + 'px';
            iframe.style.height = window.innerHeight + 'px';
            document.body.appendChild(iframe);

            iframe.onload = function() {
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    const searchBar = iframeDoc.querySelector('.search-box');
                    const appMenu = iframeDoc.querySelector('.app-menu');
                    const mainContent = iframeDoc.querySelector('.main-content');

                    console.log('🔍 主应用元素检测:', {
                        searchBarHeight: searchBar ? searchBar.offsetHeight : '未找到',
                        appMenuHeight: appMenu ? appMenu.offsetHeight : '未找到',
                        mainContentHeight: mainContent ? mainContent.offsetHeight : '未找到',
                        windowHeight: window.innerHeight
                    });

                    // 更新调试信息
                    if (searchBar && appMenu) {
                        const totalReservedHeight = searchBar.offsetHeight + appMenu.offsetHeight;
                        const expectedContentHeight = window.innerHeight - totalReservedHeight;

                        const debugSection = document.querySelector('.debug-section');
                        const newInfo = document.createElement('div');
                        newInfo.className = 'test-content';
                        newInfo.innerHTML = `
                            <h4>🎯 主应用元素高度检测</h4>
                            <p>搜索栏高度: ${searchBar.offsetHeight}px</p>
                            <p>底部导航高度: ${appMenu.offsetHeight}px</p>
                            <p>预留总高度: ${totalReservedHeight}px</p>
                            <p>期望内容高度: ${expectedContentHeight}px</p>
                            <p>实际内容高度: ${mainContent ? mainContent.offsetHeight : '未检测到'}px</p>
                        `;
                        debugSection.appendChild(newInfo);
                    }

                } catch (e) {
                    console.log('⚠️ 无法访问主应用iframe内容（跨域限制）');
                }

                // 清理iframe
                setTimeout(() => {
                    document.body.removeChild(iframe);
                }, 1000);
            };
        }

        // 初始化
        runDiagnostics();

        // 监听变化
        window.addEventListener('resize', () => {
            setTimeout(runDiagnostics, 100);
        });

        window.addEventListener('orientationchange', () => {
            setTimeout(runDiagnostics, 500);
        });

        // 导航按钮点击事件
        document.querySelectorAll('.nav-button').forEach((btn, index) => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.nav-button').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
            });
        });

        console.log('🔧 移动端调试工具已加载');
    </script>
</body>
</html>
