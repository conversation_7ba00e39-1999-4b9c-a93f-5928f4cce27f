<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    
    <!-- PWA 配置 -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="Tabbar测试">
    
    <title>底部导航栏测试</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            height: 100vh;
            height: 100dvh;
            overflow: hidden;
        }
        
        .container {
            height: calc(100vh - 60px);
            height: calc(100dvh - 60px);
            height: calc(var(--vh, 1vh) * 100 - 60px);
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .subtitle {
            font-size: 16px;
            opacity: 0.8;
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }
        
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .test-item:last-child {
            border-bottom: none;
        }
        
        .test-label {
            font-size: 14px;
        }
        
        .test-result {
            font-weight: bold;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
        }
        
        .test-result.pass {
            background: #4ade80;
            color: #000;
        }
        
        .test-result.fail {
            background: #f87171;
            color: #fff;
        }
        
        .content-area {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
            min-height: 200px;
        }
        
        /* 模拟随心听的底部导航栏 */
        .app-menu {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            width: 100%;
            height: 60px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            display: flex;
            align-items: center;
            justify-content: space-around;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            z-index: 99999;
            padding-bottom: env(safe-area-inset-bottom);
        }
        
        .app-menu-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px;
            color: #666;
            font-size: 12px;
            text-decoration: none;
            flex: 1;
        }
        
        .app-menu-item.active {
            color: #10B981;
        }
        
        .app-menu-item-icon {
            font-size: 24px;
            margin-bottom: 4px;
        }
        
        /* iPad 特殊处理 */
        @media screen and (min-width: 768px) and (max-width: 1024px) {
            .container {
                height: calc(100vh - 60px);
                height: calc(100dvh - 60px);
                height: calc(var(--vh, 1vh) * 100 - 60px);
            }
            
            .app-menu {
                display: flex !important;
                height: 60px !important;
            }
        }
        
        /* 深色模式 */
        @media (prefers-color-scheme: dark) {
            .app-menu {
                background: rgba(0, 0, 0, 0.95);
                border-top: 1px solid rgba(255, 255, 255, 0.1);
            }
            
            .app-menu-item {
                color: #ccc;
            }
            
            .app-menu-item.active {
                color: #10B981;
            }
        }
        
        /* PWA模式指示器 */
        .pwa-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4ade80;
            color: #000;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            display: none;
        }
        
        @media (display-mode: standalone) {
            .pwa-indicator {
                display: block;
            }
        }
    </style>
</head>
<body>
    <div class="pwa-indicator">
        🚀 PWA 模式
    </div>
    
    <div class="container">
        <div class="header">
            <div class="title">🎵 底部导航栏测试</div>
            <div class="subtitle">验证移动端和iPad的tabbar显示</div>
        </div>
        
        <div class="test-section">
            <div class="test-title">📱 设备信息</div>
            <div class="test-item">
                <div class="test-label">设备类型</div>
                <div class="test-result" id="deviceType">检测中...</div>
            </div>
            <div class="test-item">
                <div class="test-label">屏幕尺寸</div>
                <div class="test-result" id="screenSize">检测中...</div>
            </div>
            <div class="test-item">
                <div class="test-label">视口尺寸</div>
                <div class="test-result" id="viewportSize">检测中...</div>
            </div>
            <div class="test-item">
                <div class="test-label">PWA状态</div>
                <div class="test-result" id="pwaStatus">检测中...</div>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">🔧 Tabbar测试结果</div>
            <div class="test-item">
                <div class="test-label">底部导航显示</div>
                <div class="test-result" id="tabbarVisible">检测中</div>
            </div>
            <div class="test-item">
                <div class="test-label">iPad兼容性</div>
                <div class="test-result" id="ipadCompatibility">检测中</div>
            </div>
            <div class="test-item">
                <div class="test-label">安全区域适配</div>
                <div class="test-result" id="safeAreaSupport">检测中</div>
            </div>
            <div class="test-item">
                <div class="test-label">毛玻璃效果</div>
                <div class="test-result" id="backdropFilter">检测中</div>
            </div>
        </div>
        
        <div class="content-area">
            <h3>📝 测试说明</h3>
            <p>这个页面模拟了随心听应用的底部导航栏，用于测试：</p>
            <ul style="margin: 10px 0; padding-left: 20px;">
                <li>iPhone上的底部导航显示</li>
                <li>iPad上的底部导航显示</li>
                <li>PWA模式下的表现</li>
                <li>安全区域适配</li>
                <li>毛玻璃背景效果</li>
            </ul>
            
            <h3 style="margin-top: 20px;">🔗 相关链接</h3>
            <p><a href="/" style="color: #4ade80;">返回主应用</a></p>
            <p><a href="/ios-pwa-fix-test.html" style="color: #4ade80;">完整PWA测试</a></p>
        </div>
        
        <div class="content-area">
            <h3>📊 实时数据</h3>
            <p>容器高度: <span id="containerHeight">-</span></p>
            <p>Tabbar高度: <span id="tabbarHeight">-</span></p>
            <p>底部安全区域: <span id="safeAreaBottom">-</span></p>
            <p>视口高度变量: <span id="vhVariable">-</span></p>
        </div>
    </div>
    
    <!-- 模拟随心听的底部导航栏 -->
    <div class="app-menu">
        <a href="#" class="app-menu-item active">
            <div class="app-menu-item-icon">🏠</div>
            <div>首页</div>
        </a>
        <a href="#" class="app-menu-item">
            <div class="app-menu-item-icon">🎵</div>
            <div>音乐</div>
        </a>
        <a href="#" class="app-menu-item">
            <div class="app-menu-item-icon">❤️</div>
            <div>收藏</div>
        </a>
        <a href="#" class="app-menu-item">
            <div class="app-menu-item-icon">👤</div>
            <div>我的</div>
        </a>
    </div>
    
    <script>
        // 设备检测
        function detectDevice() {
            const userAgent = navigator.userAgent;
            const isIOS = /iPad|iPhone|iPod/.test(userAgent);
            const isIPad = /iPad/.test(userAgent) || 
                           (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
            const isIPhone = /iPhone/.test(userAgent);
            const isAndroid = /Android/.test(userAgent);
            const isPWA = window.matchMedia('(display-mode: standalone)').matches ||
                          window.navigator.standalone === true;
            
            return {
                isIOS,
                isIPad,
                isIPhone,
                isAndroid,
                isPWA
            };
        }
        
        // 设置视口高度变量
        function setViewportHeight() {
            const vh = window.innerHeight * 0.01;
            document.documentElement.style.setProperty('--vh', `${vh}px`);
        }
        
        // 运行测试
        function runTests() {
            const device = detectDevice();
            
            // 设备类型
            let deviceTypeText = '';
            if (device.isIPad) deviceTypeText = 'iPad';
            else if (device.isIPhone) deviceTypeText = 'iPhone';
            else if (device.isAndroid) deviceTypeText = 'Android';
            else deviceTypeText = '桌面';
            
            document.getElementById('deviceType').textContent = deviceTypeText;
            document.getElementById('deviceType').className = 'test-result pass';
            
            // 屏幕和视口尺寸
            document.getElementById('screenSize').textContent = `${screen.width}×${screen.height}`;
            document.getElementById('screenSize').className = 'test-result pass';
            
            document.getElementById('viewportSize').textContent = `${window.innerWidth}×${window.innerHeight}`;
            document.getElementById('viewportSize').className = 'test-result pass';
            
            // PWA 状态
            const pwaStatusEl = document.getElementById('pwaStatus');
            if (device.isPWA) {
                pwaStatusEl.textContent = 'PWA模式';
                pwaStatusEl.className = 'test-result pass';
            } else {
                pwaStatusEl.textContent = '浏览器模式';
                pwaStatusEl.className = 'test-result';
            }
            
            // Tabbar显示测试
            const tabbar = document.querySelector('.app-menu');
            const tabbarVisible = window.getComputedStyle(tabbar).display !== 'none';
            const tabbarVisibleEl = document.getElementById('tabbarVisible');
            
            if (tabbarVisible) {
                tabbarVisibleEl.textContent = 'PASS';
                tabbarVisibleEl.className = 'test-result pass';
            } else {
                tabbarVisibleEl.textContent = 'FAIL';
                tabbarVisibleEl.className = 'test-result fail';
            }
            
            // iPad兼容性测试
            const ipadCompatibilityEl = document.getElementById('ipadCompatibility');
            if (!device.isIPad) {
                ipadCompatibilityEl.textContent = 'N/A';
                ipadCompatibilityEl.className = 'test-result';
            } else if (tabbarVisible) {
                ipadCompatibilityEl.textContent = 'PASS';
                ipadCompatibilityEl.className = 'test-result pass';
            } else {
                ipadCompatibilityEl.textContent = 'FAIL';
                ipadCompatibilityEl.className = 'test-result fail';
            }
            
            // 安全区域支持
            const safeAreaSupportEl = document.getElementById('safeAreaSupport');
            const hasEnvSupport = CSS.supports('padding', 'env(safe-area-inset-bottom)');
            if (hasEnvSupport) {
                safeAreaSupportEl.textContent = 'PASS';
                safeAreaSupportEl.className = 'test-result pass';
            } else {
                safeAreaSupportEl.textContent = 'FAIL';
                safeAreaSupportEl.className = 'test-result fail';
            }
            
            // 毛玻璃效果
            const backdropFilterEl = document.getElementById('backdropFilter');
            const hasBackdropFilter = CSS.supports('backdrop-filter', 'blur(20px)');
            if (hasBackdropFilter) {
                backdropFilterEl.textContent = 'PASS';
                backdropFilterEl.className = 'test-result pass';
            } else {
                backdropFilterEl.textContent = 'FAIL';
                backdropFilterEl.className = 'test-result fail';
            }
            
            // 实时数据
            const container = document.querySelector('.container');
            document.getElementById('containerHeight').textContent = container.offsetHeight + 'px';
            document.getElementById('tabbarHeight').textContent = tabbar.offsetHeight + 'px';
            
            const vhValue = getComputedStyle(document.documentElement).getPropertyValue('--vh');
            document.getElementById('vhVariable').textContent = vhValue || '未设置';
            
            // 安全区域
            const safeAreaBottom = getComputedStyle(tabbar).paddingBottom;
            document.getElementById('safeAreaBottom').textContent = safeAreaBottom;
        }
        
        // 初始化
        setViewportHeight();
        runTests();
        
        // 监听变化
        window.addEventListener('resize', () => {
            setViewportHeight();
            setTimeout(runTests, 100);
        });
        
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                setViewportHeight();
                runTests();
            }, 500);
        });
        
        if (window.visualViewport) {
            window.visualViewport.addEventListener('resize', () => {
                setViewportHeight();
                setTimeout(runTests, 100);
            });
        }
        
        console.log('🧪 Tabbar测试页面已加载');
        console.log('设备信息:', detectDevice());
    </script>
</body>
</html>
