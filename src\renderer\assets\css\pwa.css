/* PWA 模式专用样式 */

/* iOS Safari PWA 模式优化 */
.pwa-mode {
  /* 确保内容填满整个屏幕 */
  height: 100vh;
  height: 100dvh; /* 动态视口高度，更准确 */
  overflow: hidden;
  position: relative;
}

/* 适配 iPhone X 系列的安全区域 */
@supports (padding: max(0px)) {
  .pwa-mode {
    padding-left: max(0px, env(safe-area-inset-left));
    padding-right: max(0px, env(safe-area-inset-right));
    padding-top: max(0px, env(safe-area-inset-top));
    padding-bottom: max(0px, env(safe-area-inset-bottom));
  }
}

/* PWA 模式下的状态栏适配 */
.pwa-mode body {
  margin: 0;
  padding: 0;
  height: 100vh;
  height: 100dvh;
  overflow: hidden;
  -webkit-overflow-scrolling: touch;
  position: fixed;
  width: 100%;
  top: 0;
  left: 0;
}

/* PWA 模式下的主容器 */
.pwa-mode #app {
  height: 100vh;
  height: 100dvh;
  overflow: hidden;
  position: relative;
  width: 100%;
}

/* 防止iOS Safari的橡皮筋效果 */
.pwa-mode html {
  height: 100%;
  overflow: hidden;
  position: fixed;
  width: 100%;
}

/* PWA 模式下的滚动容器 */
.pwa-mode .scroll-container {
  height: 100%;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: none;
}

/* 优化触摸反馈 */
.pwa-mode button,
.pwa-mode .clickable {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

/* PWA 模式下的导航栏适配 */
.pwa-mode .navbar {
  padding-top: env(safe-area-inset-top);
  background: var(--bg-color, #ffffff);
}

/* PWA 模式下的底部播放器适配 */
.pwa-mode .player-bar {
  padding-bottom: env(safe-area-inset-bottom);
  background: var(--bg-color, #ffffff);
}

/* 全屏模式下的特殊处理 */
.pwa-mode.fullscreen {
  padding: 0 !important;
}

/* 横屏模式适配 */
@media screen and (orientation: landscape) {
  .pwa-mode {
    padding-left: max(20px, env(safe-area-inset-left));
    padding-right: max(20px, env(safe-area-inset-right));
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .pwa-mode {
    background-color: #1a1a1a;
    color: #ffffff;
  }
  
  .pwa-mode .navbar,
  .pwa-mode .player-bar {
    background: #1a1a1a;
  }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2) {
  .pwa-mode {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

/* PWA 启动动画 */
.pwa-mode .app-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--primary-color, #4CAF50);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  opacity: 1;
  transition: opacity 0.5s ease-out;
}

.pwa-mode .app-loading.fade-out {
  opacity: 0;
  pointer-events: none;
}

.pwa-mode .app-loading .logo {
  width: 80px;
  height: 80px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 防止文本选择 */
.pwa-mode {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 允许输入框文本选择 */
.pwa-mode input,
.pwa-mode textarea {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

/* PWA 模式下的特殊按钮样式 */
.pwa-mode .pwa-install-button {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: var(--primary-color, #4CAF50);
  color: white;
  border: none;
  border-radius: 50px;
  padding: 12px 20px;
  font-size: 14px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  display: none;
}

.pwa-mode .pwa-install-button.show {
  display: block;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 状态栏颜色适配 */
.pwa-mode[data-theme="light"] {
  --status-bar-color: #ffffff;
}

.pwa-mode[data-theme="dark"] {
  --status-bar-color: #1a1a1a;
}

/* 确保内容不被状态栏遮挡 */
.pwa-mode .main-content {
  padding-top: env(safe-area-inset-top);
  height: calc(100vh - env(safe-area-inset-top) - env(safe-area-inset-bottom));
  height: calc(100dvh - env(safe-area-inset-top) - env(safe-area-inset-bottom));
  overflow-y: auto;
}

/* iOS 特定的滚动优化 */
.pwa-mode .scrollable {
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}

/* 防止页面弹跳 */
.pwa-mode body {
  overscroll-behavior: none;
}

/* 强制全屏模式样式 */
.pwa-mode {
  /* 隐藏地址栏的关键样式 */
  min-height: 100vh;
  min-height: 100dvh;
  position: relative;
}

/* 确保PWA模式下完全全屏 - 修复移动端显示问题 */
@media all and (display-mode: standalone) {
  html, body {
    height: 100vh !important;
    height: 100dvh !important;
    margin: 0 !important;
    padding: 0 !important;
    width: 100% !important;
    /* 移除 overflow: hidden 和 position: fixed，避免内容被遮挡 */
  }

  #app {
    height: 100vh !important;
    height: 100dvh !important;
    position: relative !important;
    width: 100% !important;
    /* 移除 overflow: hidden，允许内容正常显示 */
  }
}

/* iOS Safari 特定的全屏样式 */
@supports (-webkit-touch-callout: none) {
  .pwa-mode {
    /* iOS Safari 专用样式 */
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -webkit-tap-highlight-color: transparent;
  }

  .pwa-mode body {
    /* 强制全屏 */
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    overflow: hidden !important;
  }
}

/* 浏览器模式下的全屏样式 */
.fullscreen-active {
  height: 100vh !important;
  height: 100dvh !important;
  overflow: hidden !important;
  position: fixed !important;
  width: 100% !important;
  top: 0 !important;
  left: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

.fullscreen-active body {
  height: 100vh !important;
  height: 100dvh !important;
  overflow: hidden !important;
  position: fixed !important;
  width: 100% !important;
  top: 0 !important;
  left: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

.fullscreen-active #app {
  height: 100vh !important;
  height: 100dvh !important;
  overflow: hidden !important;
  position: relative !important;
  width: 100% !important;
}

/* 强制隐藏滚动条 */
.fullscreen-active::-webkit-scrollbar,
.fullscreen-active body::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

.fullscreen-active,
.fullscreen-active body {
  -ms-overflow-style: none !important;
  scrollbar-width: none !important;
}

/* 防止任何形式的滚动 */
.fullscreen-active,
.fullscreen-active body {
  overscroll-behavior: none !important;
  -webkit-overflow-scrolling: auto !important;
  touch-action: manipulation !important;
}

/* 移动端特殊处理 */
@media screen and (max-width: 768px) {
  .fullscreen-active {
    /* 移动端强制全屏 */
    min-height: 100vh !important;
    min-height: 100dvh !important;
    max-height: 100vh !important;
    max-height: 100dvh !important;
  }

  .fullscreen-active body {
    min-height: 100vh !important;
    min-height: 100dvh !important;
    max-height: 100vh !important;
    max-height: 100dvh !important;
  }
}

/* iOS Safari 专用地址栏隐藏样式 */
@supports (-webkit-touch-callout: none) {
  /* iOS 设备检测 */
  html {
    height: 100vh !important;
    height: 100dvh !important;
    height: calc(var(--vh, 1vh) * 100) !important;
    width: 100vw !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    overflow: hidden !important;
    -webkit-overflow-scrolling: auto !important;
    -webkit-text-size-adjust: 100% !important;
    -webkit-user-select: none !important;
    -webkit-touch-callout: none !important;
    -webkit-tap-highlight-color: transparent !important;
  }

  body {
    height: 100vh !important;
    height: 100dvh !important;
    height: calc(var(--vh, 1vh) * 100) !important;
    width: 100vw !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    overflow: hidden !important;
    -webkit-overflow-scrolling: auto !important;
    overscroll-behavior: none !important;
    -webkit-user-select: none !important;
    -webkit-touch-callout: none !important;
    -webkit-tap-highlight-color: transparent !important;
  }

  #app {
    height: 100vh !important;
    height: 100dvh !important;
    height: calc(var(--vh, 1vh) * 100) !important;
    width: 100vw !important;
    position: relative !important;
    top: 0 !important;
    left: 0 !important;
    overflow: hidden !important;
    -webkit-overflow-scrolling: auto !important;
  }

  /* 防止任何形式的滚动和缩放 */
  * {
    -webkit-overflow-scrolling: auto !important;
    overscroll-behavior: none !important;
    -webkit-user-select: none !important;
    -webkit-touch-callout: none !important;
    -webkit-tap-highlight-color: transparent !important;
  }

  /* 隐藏滚动条 */
  ::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
  }
}

/* iOS Safari 地址栏隐藏专用 */
@media screen and (max-device-width: 812px) and (-webkit-min-device-pixel-ratio: 2) {
  /* iPhone 专用样式 */
  html, body {
    position: fixed !important;
    overflow: hidden !important;
    height: 100% !important;
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  #app {
    position: relative !important;
    height: 100% !important;
    width: 100% !important;
    overflow: hidden !important;
  }
}

/* 强制全屏模式 - 最高优先级 */
.ios-fullscreen-mode {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  height: 100dvh !important;
  overflow: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
  -webkit-overflow-scrolling: auto !important;
  overscroll-behavior: none !important;
}

.ios-fullscreen-mode body {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  height: 100dvh !important;
  overflow: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
  -webkit-overflow-scrolling: auto !important;
  overscroll-behavior: none !important;
}

.ios-fullscreen-mode #app {
  position: relative !important;
  width: 100vw !important;
  height: 100vh !important;
  height: 100dvh !important;
  overflow: hidden !important;
  top: 0 !important;
  left: 0 !important;
}
