<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <!-- 完全参考dy.521668.xyz的viewport配置 -->
    <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    
    <!-- 移动设备全屏显示相关元标签 - 参考完美PWA -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="theme-color" content="#4CAF50">
    <meta name="application-name" content="随心听测试">
    <meta name="apple-mobile-web-app-title" content="随心听测试">
    
    <!-- 添加全面屏支持 -->
    <meta name="apple-touch-fullscreen" content="yes">
    <!-- 消除底部空白区域 -->
    <meta name="apple-mobile-web-app-status-bar-inset" content="black-translucent">
    
    <!-- 防止电话号码自动识别 -->
    <meta name="format-detection" content="telephone=no, date=no, address=no, email=no">
    
    <title>完美PWA测试 - 随心听</title>
    
    <style>
        /* 完全参考dy.521668.xyz的CSS样式 */
        :root {
            --primary-color: #4CAF50;
            --secondary-color: #45a049;
            --text-color: #ffffff;
            --background-color: rgba(18, 18, 18, 0.7);
            --card-background: rgba(30, 30, 30, 0.6);
            --border-radius: 10px;
            --safe-area-inset-bottom: env(safe-area-inset-bottom, 0px);
            --safe-area-inset-top: env(safe-area-inset-top, 0px);
            --app-height: 100vh;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }

        html, body {
            height: 100vh;
            width: 100vw;
            margin: 0;
            padding: 0;
            background: #000;
            overflow-x: hidden;
            font-family: 'Helvetica Neue', Arial, sans-serif;
            color: var(--text-color);
        }

        /* 背景图层 */
        .bg-image {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            background-size: cover;
            background-position: center center;
            background-repeat: no-repeat;
            z-index: -2;
        }

        /* 背景遮罩层 */
        .bg-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--background-color);
            backdrop-filter: blur(3px);
            z-index: -1;
        }

        /* 主容器 - 完全参考完美网站 */
        #main-container {
            height: calc(100vh - 60px);
            width: 100vw;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding: 20px;
            padding-bottom: 20px;
            padding-top: calc(20px + var(--safe-area-inset-top));
            scrollbar-width: none;
            position: relative;
            z-index: 1;
            box-sizing: border-box;
        }

        #main-container::-webkit-scrollbar {
            display: none;
        }

        /* 底部导航栏 - 完全参考完美网站 */
        #bottom-nav {
            position: fixed;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100vw;
            height: 60px;
            padding-bottom: env(safe-area-inset-bottom, 0px);
            background: rgba(18, 18, 18, 0.98);
            z-index: 1000;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid rgba(51, 51, 51, 0.2);
            box-shadow: 0 -1px 8px rgba(0, 0, 0, 0.2);
        }

        /* 导航按钮样式 - 完全参考完美网站 */
        .nav-button {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: rgba(255, 255, 255, 0.6);
            font-size: 12px;
            padding: 8px 0;
            transition: all 0.3s;
            cursor: pointer;
            width: 25%;
        }

        .nav-button i {
            font-size: 24px;
            margin-bottom: 4px;
        }

        .nav-button.active {
            color: var(--primary-color);
            transform: scale(1.05);
            font-weight: 500;
            text-shadow: 0 0 10px rgba(76, 175, 80, 0.4);
        }

        .nav-button.active i {
            text-shadow: 0 0 15px rgba(76, 175, 80, 0.6);
        }

        /* PWA模式特殊样式 */
        .pwa-mode {
            overflow: hidden;
            background-color: #000;
        }

        /* 确保在全屏模式下正确显示 */
        @media screen and (display-mode: standalone), 
               screen and (display-mode: fullscreen) {
            body {
                height: var(--app-height);
                background-color: #000;
            }
            
            html {
                background-color: #000;
                height: var(--app-height);
            }
            
            #main-container {
                height: calc(var(--app-height) - 60px - var(--safe-area-inset-bottom));
                padding-bottom: calc(70px + var(--safe-area-inset-bottom));
            }
            
            #bottom-nav {
                bottom: 0;
            }
        }

        /* 内容样式 */
        .content-section {
            background: var(--card-background);
            border-radius: var(--border-radius);
            padding: 20px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }

        .title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 10px;
            color: var(--primary-color);
            text-align: center;
        }

        .subtitle {
            font-size: 16px;
            opacity: 0.8;
            text-align: center;
            margin-bottom: 30px;
        }

        .test-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .test-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }

        .test-label {
            font-size: 14px;
            opacity: 0.8;
            margin-bottom: 8px;
        }

        .test-value {
            font-size: 18px;
            font-weight: bold;
            color: var(--primary-color);
        }

        .pwa-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--primary-color);
            color: #000;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            display: none;
        }

        @media (display-mode: standalone) {
            .pwa-indicator {
                display: block;
            }
        }
    </style>
</head>
<body>
    <div class="pwa-indicator">
        🚀 PWA 模式
    </div>
    
    <!-- 背景图层和遮罩层 -->
    <div class="bg-image"></div>
    <div class="bg-overlay"></div>

    <!-- 主要内容区域 -->
    <div id="main-container">
        <div class="content-section">
            <div class="title">🎵 完美PWA测试</div>
            <div class="subtitle">参考dy.521668.xyz的完美实现</div>
            
            <div class="test-grid">
                <div class="test-card">
                    <div class="test-label">设备类型</div>
                    <div class="test-value" id="deviceType">检测中...</div>
                </div>
                <div class="test-card">
                    <div class="test-label">PWA状态</div>
                    <div class="test-value" id="pwaStatus">检测中...</div>
                </div>
                <div class="test-card">
                    <div class="test-label">视口高度</div>
                    <div class="test-value" id="viewportHeight">检测中...</div>
                </div>
                <div class="test-card">
                    <div class="test-label">底部导航</div>
                    <div class="test-value" id="tabbarStatus">正常显示</div>
                </div>
            </div>
        </div>

        <div class="content-section">
            <h3>📊 完美PWA特性验证</h3>
            <p>✅ 完全参考dy.521668.xyz的PWA实现</p>
            <p>✅ 底部导航栏始终显示</p>
            <p>✅ 安全区域完美适配</p>
            <p>✅ PWA模式全屏体验</p>
            <p>✅ 滚动性能优化</p>
            
            <h3 style="margin-top: 20px;">🔗 测试链接</h3>
            <p><a href="/" style="color: var(--primary-color);">返回主应用</a></p>
            <p><a href="/tabbar-test.html" style="color: var(--primary-color);">Tabbar专用测试</a></p>
        </div>

        <div class="content-section">
            <h3>📱 使用说明</h3>
            <p><strong>浏览器模式:</strong> 直接访问，底部导航正常显示</p>
            <p><strong>PWA模式:</strong> 添加到主屏幕，完全全屏体验</p>
            <p><strong>iPad适配:</strong> 自动适配iPad屏幕尺寸</p>
        </div>
    </div>
    
    <!-- 底部导航栏 - 完全参考完美网站 -->
    <div id="bottom-nav">
        <div class="nav-button active">
            <i>🏠</i>
            <span>首页</span>
        </div>
        <div class="nav-button">
            <i>🔍</i>
            <span>搜索</span>
        </div>
        <div class="nav-button">
            <i>❤️</i>
            <span>收藏</span>
        </div>
        <div class="nav-button">
            <i>👤</i>
            <span>我的</span>
        </div>
    </div>

    <script>
        // 设备检测
        function detectDevice() {
            const userAgent = navigator.userAgent;
            const isIOS = /iPad|iPhone|iPod/.test(userAgent);
            const isIPad = /iPad/.test(userAgent) || 
                           (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
            const isIPhone = /iPhone/.test(userAgent);
            const isPWA = window.matchMedia('(display-mode: standalone)').matches ||
                          window.navigator.standalone === true;
            
            return { isIOS, isIPad, isIPhone, isPWA };
        }

        // 设置视口高度变量 - 参考完美网站
        function setPerfectViewportHeight() {
            const vh = window.innerHeight * 0.01;
            document.documentElement.style.setProperty('--vh', `${vh}px`);
            document.documentElement.style.setProperty('--app-height', `${window.innerHeight}px`);
            
            if (window.visualViewport) {
                const visualVh = window.visualViewport.height * 0.01;
                document.documentElement.style.setProperty('--vh', `${visualVh}px`);
                document.documentElement.style.setProperty('--app-height', `${window.visualViewport.height}px`);
            }
        }

        // 运行测试
        function runTests() {
            const device = detectDevice();
            
            // 设备类型
            let deviceTypeText = '';
            if (device.isIPad) deviceTypeText = 'iPad';
            else if (device.isIPhone) deviceTypeText = 'iPhone';
            else deviceTypeText = '其他';
            
            document.getElementById('deviceType').textContent = deviceTypeText;
            
            // PWA状态
            document.getElementById('pwaStatus').textContent = device.isPWA ? 'PWA模式' : '浏览器模式';
            
            // 视口高度
            document.getElementById('viewportHeight').textContent = window.innerHeight + 'px';
            
            console.log('🎯 完美PWA测试结果:', device);
        }

        // 初始化
        setPerfectViewportHeight();
        runTests();

        // 监听变化
        window.addEventListener('resize', () => {
            setPerfectViewportHeight();
            setTimeout(runTests, 100);
        });

        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                setPerfectViewportHeight();
                runTests();
            }, 500);
        });

        if (window.visualViewport) {
            window.visualViewport.addEventListener('resize', () => {
                setPerfectViewportHeight();
                setTimeout(runTests, 100);
            });
        }

        // 导航按钮点击事件
        document.querySelectorAll('.nav-button').forEach((btn, index) => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.nav-button').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
            });
        });

        console.log('🎯 完美PWA测试页面已加载 (参考dy.521668.xyz)');
    </script>
</body>
</html>
