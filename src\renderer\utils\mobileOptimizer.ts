/**
 * 移动端优化工具
 * 专门解决iOS PWA和移动端浏览器的各种问题
 */

// 设备检测
export function getDeviceInfo() {
  const userAgent = navigator.userAgent;
  const isIOS = /iPad|iPhone|iPod/.test(userAgent);
  const isAndroid = /Android/.test(userAgent);
  const isSafari = /^((?!chrome|android).)*safari/i.test(userAgent);
  const isPWA = window.matchMedia('(display-mode: standalone)').matches ||
                (window.navigator as any).standalone === true;
  const isIPad = /iPad/.test(userAgent) || 
                 (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
  const isMobile = window.innerWidth < 768 || 
                   /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);

  return {
    isIOS,
    isAndroid,
    isSafari,
    isPWA,
    isIPad,
    isMobile,
    isIOSSafari: isIOS && isSafari,
    screenWidth: screen.width,
    screenHeight: screen.height,
    windowWidth: window.innerWidth,
    windowHeight: window.innerHeight,
    visualViewportHeight: window.visualViewport?.height,
    devicePixelRatio: window.devicePixelRatio
  };
}

// 设置视口高度变量
export function setViewportHeight() {
  const vh = window.innerHeight * 0.01;
  document.documentElement.style.setProperty('--vh', `${vh}px`);
  
  if (window.visualViewport) {
    const visualVh = window.visualViewport.height * 0.01;
    document.documentElement.style.setProperty('--vh', `${visualVh}px`);
  }
}

// 应用移动端优化样式
export function applyMobileOptimizations() {
  const device = getDeviceInfo();
  
  console.log('📱 应用移动端优化:', device);

  // 添加设备类型类
  document.documentElement.classList.add('mobile-optimized');
  
  if (device.isIOS) {
    document.documentElement.classList.add('ios-device');
  }
  
  if (device.isIPad) {
    document.documentElement.classList.add('ipad-device');
  }
  
  if (device.isPWA) {
    document.documentElement.classList.add('pwa-mode');
  }

  // 注入移动端优化样式
  const optimizationStyles = `
    /* 移动端基础优化 */
    .mobile-optimized {
      -webkit-text-size-adjust: 100%;
      -webkit-tap-highlight-color: transparent;
      -webkit-touch-callout: none;
    }

    .mobile-optimized * {
      -webkit-transform: translateZ(0);
      transform: translateZ(0);
      -webkit-backface-visibility: hidden;
      backface-visibility: hidden;
    }

    /* 禁用双击缩放 */
    .mobile-optimized * {
      touch-action: manipulation;
      -webkit-user-select: none;
      user-select: none;
    }

    /* 允许文本选择的元素 */
    .mobile-optimized input,
    .mobile-optimized textarea,
    .mobile-optimized [contenteditable],
    .mobile-optimized .selectable {
      -webkit-user-select: auto;
      user-select: auto;
      touch-action: auto;
    }

    /* iOS 特殊处理 */
    .ios-device {
      -webkit-overflow-scrolling: touch;
    }

    .ios-device body {
      overscroll-behavior: none;
    }

    /* iPad 特殊处理 - 确保显示底部导航 */
    .ipad-device.mobile .app-menu {
      display: block;
    }

    /* PWA 模式优化 */
    .pwa-mode {
      height: 100vh !important;
      height: 100dvh !important;
      overflow: hidden !important;
    }

    .pwa-mode body {
      position: fixed !important;
      width: 100% !important;
      height: 100% !important;
      overflow: hidden !important;
    }

    /* 深色模式适配 */
    @media (prefers-color-scheme: dark) {
      .ipad-device.mobile .app-menu {
        background: rgba(0, 0, 0, 0.98) !important;
        border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
      }
    }

    /* 滚动优化 */
    .mobile-optimized .n-scrollbar,
    .mobile-optimized .main-content,
    .mobile-optimized .mobile-content {
      -webkit-overflow-scrolling: touch !important;
      overscroll-behavior: contain !important;
    }

    /* 性能优化 */
    .mobile-optimized .app-menu,
    .mobile-optimized .main-content {
      will-change: transform;
    }
  `;

  // 移除旧样式
  const oldStyle = document.getElementById('mobile-optimization-styles');
  if (oldStyle) oldStyle.remove();

  // 添加新样式
  const styleSheet = document.createElement('style');
  styleSheet.id = 'mobile-optimization-styles';
  styleSheet.textContent = optimizationStyles;
  document.head.appendChild(styleSheet);

  // 设置视口高度
  setViewportHeight();

  // 监听视口变化
  window.addEventListener('resize', setViewportHeight);
  window.addEventListener('orientationchange', () => {
    setTimeout(setViewportHeight, 100);
  });

  if (window.visualViewport) {
    window.visualViewport.addEventListener('resize', setViewportHeight);
  }

  console.log('✅ 移动端优化应用完成');
}

// 修复iPad底部导航栏问题
export function fixIPadTabbar() {
  const device = getDeviceInfo();
  
  if (!device.isIPad) return;

  console.log('🔧 修复iPad底部导航栏问题');

  // 强制显示底部导航栏
  const forceTabbarStyles = `
    /* iPad 强制显示底部导航栏 */
    @media screen and (min-width: 768px) and (max-width: 1024px) {
      .mobile .app-menu {
        display: flex !important;
        position: fixed !important;
        bottom: 0 !important;
        left: 0 !important;
        right: 0 !important;
        width: 100% !important;
        height: 60px !important;
        z-index: 99999 !important;
        background: rgba(255, 255, 255, 0.98) !important;
        backdrop-filter: blur(20px) !important;
        -webkit-backdrop-filter: blur(20px) !important;
        border-top: 1px solid rgba(0, 0, 0, 0.1) !important;
      }

      .mobile .app-menu .app-menu-list {
        display: flex !important;
        justify-content: space-around !important;
        align-items: center !important;
        height: 60px !important;
        width: 100% !important;
        padding: 0 20px !important;
      }

      .mobile .main-content,
      .mobile .mobile-content {
        height: calc(100vh - 60px) !important;
        height: calc(100dvh - 60px) !important;
        height: calc(var(--vh, 1vh) * 100 - 60px) !important;
        margin-bottom: 60px !important;
      }
    }

    /* 深色模式 */
    @media (prefers-color-scheme: dark) and (min-width: 768px) and (max-width: 1024px) {
      .mobile .app-menu {
        background: rgba(0, 0, 0, 0.98) !important;
        border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
      }
    }
  `;

  const styleSheet = document.createElement('style');
  styleSheet.id = 'ipad-tabbar-fix';
  styleSheet.textContent = forceTabbarStyles;
  document.head.appendChild(styleSheet);

  console.log('✅ iPad底部导航栏修复完成');
}

// 初始化移动端优化
export function initMobileOptimizations() {
  console.log('🚀 初始化移动端优化...');

  // 应用基础优化
  applyMobileOptimizations();

  // 修复iPad问题
  fixIPadTabbar();

  // 防止页面弹跳
  document.addEventListener('touchmove', (e) => {
    if (e.scale !== 1) {
      e.preventDefault();
    }
  }, { passive: false });

  // 防止双击缩放
  let lastTouchEnd = 0;
  document.addEventListener('touchend', (e) => {
    const now = Date.now();
    if (now - lastTouchEnd <= 300) {
      e.preventDefault();
    }
    lastTouchEnd = now;
  }, false);

  console.log('✅ 移动端优化初始化完成');
}
