<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.ico" />
    
    <!-- 参考完美PWA网站的viewport配置 -->
    <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">

    <!-- 移动设备全屏显示相关元标签 - 完全匹配完美PWA网站 -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="theme-color" content="#4CAF50">
    <meta name="application-name" content="随心听">
    <meta name="apple-mobile-web-app-title" content="随心听">

    <!-- 添加全面屏支持 -->
    <meta name="apple-touch-fullscreen" content="yes">

    <!-- 消除底部空白区域 -->
    <meta name="apple-mobile-web-app-status-bar-inset" content="black-translucent">

    <!-- iOS PWA 关键标签 - 确保全屏显示 -->
    <meta name="HandheldFriendly" content="true">
    <meta name="MobileOptimized" content="width">
    <meta name="full-screen" content="yes">
    <meta name="browsermode" content="application">
    <meta name="x5-fullscreen" content="true">
    <meta name="x5-page-mode" content="app">
    <meta name="x5-orientation" content="portrait">
    <meta name="screen-orientation" content="portrait">

    <!-- 防止电话号码自动识别 -->
    <meta name="format-detection" content="telephone=no, date=no, address=no, email=no">
    
    <!-- 确保PWA全屏模式 -->
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="msapplication-TileColor" content="#4CAF50">

    <!-- SEO 元数据 -->
    <title>随心听</title>
    <meta name="description" content="随心听-随时随地，好音乐不等待 - 一款免费的在线音乐播放器，支持在线播放、歌词显示、音乐下载等功能。提供海量音乐资源，让您随时随地享受音乐。" />
    <meta name="keywords" content="随心听, 音乐播放器, 在线音乐, 免费音乐, 歌词显示, 音乐下载, 网易云音乐" />

    <!-- 作者信息 -->
    <meta name="author" content="随心听" />
    <meta name="author-url" content="https://github.com/algerkong" />

    <!-- PWA 相关 -->
    <link rel="manifest" href="/manifest.json" />
    <meta name="theme-color" content="#4CAF50" />
    <meta name="theme-color" media="(prefers-color-scheme: light)" content="#4CAF50" />
    <meta name="theme-color" media="(prefers-color-scheme: dark)" content="#2E7D32" />

    <!-- Apple Touch Icons - 完整的iOS PWA图标配置 -->
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
    <link rel="apple-touch-icon" sizes="57x57" href="/icons/icon-72x72.png" />
    <link rel="apple-touch-icon" sizes="60x60" href="/icons/icon-72x72.png" />
    <link rel="apple-touch-icon" sizes="72x72" href="/icons/icon-72x72.png" />
    <link rel="apple-touch-icon" sizes="76x76" href="/icons/icon-96x96.png" />
    <link rel="apple-touch-icon" sizes="114x114" href="/icons/icon-144x144.png" />
    <link rel="apple-touch-icon" sizes="120x120" href="/icons/icon-144x144.png" />
    <link rel="apple-touch-icon" sizes="144x144" href="/icons/icon-144x144.png" />
    <link rel="apple-touch-icon" sizes="152x152" href="/icons/icon-152x152.png" />
    <link rel="apple-touch-icon" sizes="167x167" href="/icons/icon-152x152.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="/icons/icon-192x192.png" />

    <!-- iOS PWA Splash Screens - 启动画面 -->
    <link rel="apple-touch-startup-image" href="/splash/default.svg" />
    <link rel="apple-touch-startup-image" media="screen and (device-width: 320px) and (device-height: 568px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)" href="/splash/default.svg" />
    <link rel="apple-touch-startup-image" media="screen and (device-width: 375px) and (device-height: 667px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)" href="/splash/default.svg" />
    <link rel="apple-touch-startup-image" media="screen and (device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)" href="/splash/default.svg" />
    <link rel="apple-touch-startup-image" media="screen and (device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)" href="/splash/default.svg" />
    <link rel="apple-touch-startup-image" media="screen and (device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)" href="/splash/default.svg" />
    <link rel="apple-touch-startup-image" media="screen and (device-width: 768px) and (device-height: 1024px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)" href="/splash/default.svg" />
    <link rel="apple-touch-startup-image" media="screen and (device-width: 834px) and (device-height: 1112px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)" href="/splash/default.svg" />
    <link rel="apple-touch-startup-image" media="screen and (device-width: 1024px) and (device-height: 1366px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)" href="/splash/default.svg" />

    <!-- 资源预加载 -->
    <link rel="preload" href="./assets/icon/iconfont.css" as="style" />
    <link rel="preload" href="./assets/css/base.css" as="style" />

    <!-- 样式表 -->
    <link rel="stylesheet" href="./assets/icon/iconfont.css" />
    <link rel="stylesheet" href="./assets/css/base.css" />
    <link rel="stylesheet" href="./assets/css/pwa.css" />

    <!-- 动画配置 -->
    <style>
      :root {
        --animate-delay: 0.5s;
      }
    </style>
  </head>

  <body>
    <div id="app"></div>
    <script type="module" src="./main.ts"></script>
    
    <!-- PWA 和 Service Worker 相关脚本 -->
    <script>
      // 注册Service Worker
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
          navigator.serviceWorker.register('/sw.js')
            .then(registration => {
              console.log('Service Worker 注册成功:', registration.scope);
            })
            .catch(error => {
              console.log('Service Worker 注册失败:', error);
            });
        });
      }

      // iOS PWA 强制全屏模式检测
      function isIOSPWA() {
        return window.navigator.standalone === true;
      }

      function isIOSDevice() {
        return /iPad|iPhone|iPod/.test(navigator.userAgent);
      }

      // 强制PWA模式
      if (isIOSDevice()) {
        // 添加PWA模式类
        document.documentElement.classList.add('ios-device');
        if (isIOSPWA()) {
          document.documentElement.classList.add('ios-pwa-mode');
          document.body.classList.add('pwa-mode');
        }

        // 防止双击缩放
        let lastTouchEnd = 0;
        document.addEventListener('touchend', function (event) {
          const now = (new Date()).getTime();
          if (now - lastTouchEnd <= 300) {
            event.preventDefault();
          }
          lastTouchEnd = now;
        }, false);

        // 防止橡皮筋效果
        document.addEventListener('touchmove', function (event) {
          if (event.scale !== 1) {
            event.preventDefault();
          }
        }, { passive: false });
      }

      // 简化的PWA优化函数 - 参考完美PWA网站
      function applyPerfectPWAStyles() {
        console.log('🎯 应用完美PWA样式 (参考dy.521668.xyz)...');

        // 检测移动端
        const isMobileDevice = window.innerWidth < 768 || 
                               /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

        if (isMobileDevice) {
          console.log('📱 添加移动端类');
          document.documentElement.classList.add('mobile');
        }

        // 设置视口高度变量 - 参考完美网站
        function setPerfectViewportHeight() {
          const vh = window.innerHeight * 0.01;
          document.documentElement.style.setProperty('--vh', `${vh}px`);
          document.documentElement.style.setProperty('--app-height', `${window.innerHeight}px`);
          
          if (window.visualViewport) {
            const visualVh = window.visualViewport.height * 0.01;
            document.documentElement.style.setProperty('--vh', `${visualVh}px`);
            document.documentElement.style.setProperty('--app-height', `${window.visualViewport.height}px`);
          }
        }

        setPerfectViewportHeight();
        window.addEventListener('resize', setPerfectViewportHeight);
        window.addEventListener('orientationchange', () => {
          setTimeout(setPerfectViewportHeight, 100);
        });

        if (window.visualViewport) {
          window.visualViewport.addEventListener('resize', setPerfectViewportHeight);
        }

        console.log('✅ 完美PWA样式应用完成');
      }

      // 页面加载完成后执行
      document.addEventListener('DOMContentLoaded', applyPerfectPWAStyles);
      
      console.log('🎵 随心听 - 完美PWA版本已加载');
    </script>
  </body>
</html>
