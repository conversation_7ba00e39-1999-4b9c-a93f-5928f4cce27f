<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover, shrink-to-fit=no"
    />

    <!-- 防止Safari自动缩放 -->
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="mobile-web-app-status-bar-style" content="black-translucent" />

    <!-- 强制全屏显示 -->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="随心听" />
    <meta name="apple-touch-fullscreen" content="yes" />

    <!-- 禁用Safari的智能应用横幅 -->
    <meta name="apple-itunes-app" content="app-id=, app-argument=" />

    <!-- 防止电话号码自动识别 -->
    <meta name="format-detection" content="telephone=no, date=no, address=no, email=no" />

    <!-- 确保PWA全屏模式 -->
    <meta name="msapplication-tap-highlight" content="no" />
    <meta name="msapplication-TileColor" content="#4CAF50" />
    <meta name="application-name" content="随心听" />

    <!-- SEO 元数据 -->
    <title>随心听</title>
    <meta
      name="description"
      content="随心听-随时随地，好音乐不等待 - 一款免费的在线音乐播放器，支持在线播放、歌词显示、音乐下载等功能。提供海量音乐资源，让您随时随地享受音乐。"
    />
    <meta
      name="keywords"
      content="随心听, 音乐播放器, 在线音乐, 免费音乐, 歌词显示, 音乐下载, 网易云音乐"
    />

    <!-- 作者信息 -->
    <meta name="author" content="随心听" />
    <meta name="author-url" content="https://github.com/algerkong" />

    <!-- PWA 相关 -->
    <link rel="manifest" href="/manifest.json" />
    <meta name="theme-color" content="#4CAF50" />
    <meta name="theme-color" media="(prefers-color-scheme: light)" content="#4CAF50" />
    <meta name="theme-color" media="(prefers-color-scheme: dark)" content="#2E7D32" />

    <!-- iOS设备相关（已在上方配置，此处删除重复） -->

    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
    <link rel="apple-touch-icon" sizes="57x57" href="/icons/icon-72x72.png" />
    <link rel="apple-touch-icon" sizes="60x60" href="/icons/icon-72x72.png" />
    <link rel="apple-touch-icon" sizes="72x72" href="/icons/icon-72x72.png" />
    <link rel="apple-touch-icon" sizes="76x76" href="/icons/icon-96x96.png" />
    <link rel="apple-touch-icon" sizes="114x114" href="/icons/icon-128x128.png" />
    <link rel="apple-touch-icon" sizes="120x120" href="/icons/icon-128x128.png" />
    <link rel="apple-touch-icon" sizes="144x144" href="/icons/icon-144x144.png" />
    <link rel="apple-touch-icon" sizes="152x152" href="/icons/icon-152x152.png" />
    <link rel="apple-touch-icon" sizes="167x167" href="/icons/icon-152x152.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="/icons/icon-192x192.png" />

    <!-- iOS启动屏幕 - 完整适配所有设备 -->
    <!-- iPhone SE (1st generation) -->
    <link rel="apple-touch-startup-image" href="/splash/iphone-se-portrait.png"
          media="(device-width: 320px) and (device-height: 568px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)" />
    <link rel="apple-touch-startup-image" href="/splash/iphone-se-landscape.png"
          media="(device-width: 320px) and (device-height: 568px) and (-webkit-device-pixel-ratio: 2) and (orientation: landscape)" />

    <!-- iPhone 6/7/8 -->
    <link rel="apple-touch-startup-image" href="/splash/iphone-6-portrait.png"
          media="(device-width: 375px) and (device-height: 667px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)" />
    <link rel="apple-touch-startup-image" href="/splash/iphone-6-landscape.png"
          media="(device-width: 375px) and (device-height: 667px) and (-webkit-device-pixel-ratio: 2) and (orientation: landscape)" />

    <!-- iPhone 6/7/8 Plus -->
    <link rel="apple-touch-startup-image" href="/splash/iphone-6-plus-portrait.png"
          media="(device-width: 414px) and (device-height: 736px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)" />
    <link rel="apple-touch-startup-image" href="/splash/iphone-6-plus-landscape.png"
          media="(device-width: 414px) and (device-height: 736px) and (-webkit-device-pixel-ratio: 3) and (orientation: landscape)" />

    <!-- iPhone X, XS, 11 Pro -->
    <link rel="apple-touch-startup-image" href="/splash/iphone-x-portrait.png"
          media="(device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)" />
    <link rel="apple-touch-startup-image" href="/splash/iphone-x-landscape.png"
          media="(device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3) and (orientation: landscape)" />

    <!-- iPhone XR, 11 -->
    <link rel="apple-touch-startup-image" href="/splash/iphone-xr-portrait.png"
          media="(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)" />
    <link rel="apple-touch-startup-image" href="/splash/iphone-xr-landscape.png"
          media="(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 2) and (orientation: landscape)" />

    <!-- iPhone XS Max, 11 Pro Max -->
    <link rel="apple-touch-startup-image" href="/splash/iphone-xs-max-portrait.png"
          media="(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)" />
    <link rel="apple-touch-startup-image" href="/splash/iphone-xs-max-landscape.png"
          media="(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 3) and (orientation: landscape)" />

    <!-- iPhone 12 mini -->
    <link rel="apple-touch-startup-image" href="/splash/iphone-12-mini-portrait.png"
          media="(device-width: 360px) and (device-height: 780px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)" />
    <link rel="apple-touch-startup-image" href="/splash/iphone-12-mini-landscape.png"
          media="(device-width: 360px) and (device-height: 780px) and (-webkit-device-pixel-ratio: 3) and (orientation: landscape)" />

    <!-- iPhone 12, 12 Pro, 13, 13 Pro, 14 -->
    <link rel="apple-touch-startup-image" href="/splash/iphone-12-portrait.png"
          media="(device-width: 390px) and (device-height: 844px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)" />
    <link rel="apple-touch-startup-image" href="/splash/iphone-12-landscape.png"
          media="(device-width: 390px) and (device-height: 844px) and (-webkit-device-pixel-ratio: 3) and (orientation: landscape)" />

    <!-- iPhone 12 Pro Max, 13 Pro Max, 14 Plus -->
    <link rel="apple-touch-startup-image" href="/splash/iphone-12-pro-max-portrait.png"
          media="(device-width: 428px) and (device-height: 926px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)" />
    <link rel="apple-touch-startup-image" href="/splash/iphone-12-pro-max-landscape.png"
          media="(device-width: 428px) and (device-height: 926px) and (-webkit-device-pixel-ratio: 3) and (orientation: landscape)" />

    <!-- iPhone 14 Pro -->
    <link rel="apple-touch-startup-image" href="/splash/iphone-14-pro-portrait.png"
          media="(device-width: 393px) and (device-height: 852px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)" />
    <link rel="apple-touch-startup-image" href="/splash/iphone-14-pro-landscape.png"
          media="(device-width: 393px) and (device-height: 852px) and (-webkit-device-pixel-ratio: 3) and (orientation: landscape)" />

    <!-- iPhone 14 Pro Max -->
    <link rel="apple-touch-startup-image" href="/splash/iphone-14-pro-max-portrait.png"
          media="(device-width: 430px) and (device-height: 932px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)" />
    <link rel="apple-touch-startup-image" href="/splash/iphone-14-pro-max-landscape.png"
          media="(device-width: 430px) and (device-height: 932px) and (-webkit-device-pixel-ratio: 3) and (orientation: landscape)" />

    <!-- iPhone 15, 15 Pro -->
    <link rel="apple-touch-startup-image" href="/splash/iphone-15-portrait.png"
          media="(device-width: 393px) and (device-height: 852px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)" />
    <link rel="apple-touch-startup-image" href="/splash/iphone-15-landscape.png"
          media="(device-width: 393px) and (device-height: 852px) and (-webkit-device-pixel-ratio: 3) and (orientation: landscape)" />

    <!-- iPhone 15 Plus, 15 Pro Max -->
    <link rel="apple-touch-startup-image" href="/splash/iphone-15-plus-portrait.png"
          media="(device-width: 430px) and (device-height: 932px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)" />
    <link rel="apple-touch-startup-image" href="/splash/iphone-15-plus-landscape.png"
          media="(device-width: 430px) and (device-height: 932px) and (-webkit-device-pixel-ratio: 3) and (orientation: landscape)" />

    <!-- iPad (9.7-inch) -->
    <link rel="apple-touch-startup-image" href="/splash/ipad-portrait.png"
          media="(device-width: 768px) and (device-height: 1024px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)" />
    <link rel="apple-touch-startup-image" href="/splash/ipad-landscape.png"
          media="(device-width: 768px) and (device-height: 1024px) and (-webkit-device-pixel-ratio: 2) and (orientation: landscape)" />

    <!-- iPad Pro (10.5-inch) -->
    <link rel="apple-touch-startup-image" href="/splash/ipad-pro-10-portrait.png"
          media="(device-width: 834px) and (device-height: 1112px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)" />
    <link rel="apple-touch-startup-image" href="/splash/ipad-pro-10-landscape.png"
          media="(device-width: 834px) and (device-height: 1112px) and (-webkit-device-pixel-ratio: 2) and (orientation: landscape)" />

    <!-- iPad Pro (11-inch) -->
    <link rel="apple-touch-startup-image" href="/splash/ipad-pro-11-portrait.png"
          media="(device-width: 834px) and (device-height: 1194px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)" />
    <link rel="apple-touch-startup-image" href="/splash/ipad-pro-11-landscape.png"
          media="(device-width: 834px) and (device-height: 1194px) and (-webkit-device-pixel-ratio: 2) and (orientation: landscape)" />

    <!-- iPad Pro (12.9-inch) -->
    <link rel="apple-touch-startup-image" href="/splash/ipad-pro-12-portrait.png"
          media="(device-width: 1024px) and (device-height: 1366px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)" />
    <link rel="apple-touch-startup-image" href="/splash/ipad-pro-12-landscape.png"
          media="(device-width: 1024px) and (device-height: 1366px) and (-webkit-device-pixel-ratio: 2) and (orientation: landscape)" />

    <!-- 通用启动屏幕（备用） -->
    <link rel="apple-touch-startup-image" href="/splash/default.png" />

    <!-- 资源预加载 -->
    <link rel="preload" href="./assets/icon/iconfont.css" as="style" />
    <link rel="preload" href="./assets/css/base.css" as="style" />

    <!-- 样式表 -->
    <link rel="stylesheet" href="./assets/icon/iconfont.css" />
    <link rel="stylesheet" href="./assets/css/base.css" />
    <link rel="stylesheet" href="./assets/css/pwa.css" />

    <!-- 动画配置 -->
    <style>
      :root {
        --animate-delay: 0.5s;
      }
    </style>
  </head>

  <body>
    <div id="app"></div>
    <script type="module" src="./main.ts"></script>
    <!-- PWA 和 Service Worker 相关脚本 -->
    <script>
      // 注册Service Worker
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
          navigator.serviceWorker.register('/sw.js')
            .then(registration => {
              console.log('Service Worker 注册成功:', registration.scope);

              // 检查更新
              registration.addEventListener('updatefound', () => {
                const newWorker = registration.installing;
                newWorker.addEventListener('statechange', () => {
                  if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                    // 有新版本可用
                    console.log('新版本可用，请刷新页面');
                  }
                });
              });
            })
            .catch(error => {
              console.log('Service Worker 注册失败:', error);
            });
        });
      }

      // iOS Safari 特定优化
      if (navigator.userAgent.match(/iPhone|iPad|iPod/i)) {
        // 防止双击缩放
        let lastTouchEnd = 0;
        document.addEventListener('touchend', function (event) {
          const now = (new Date()).getTime();
          if (now - lastTouchEnd <= 300) {
            event.preventDefault();
          }
          lastTouchEnd = now;
        }, false);

        // 防止橡皮筋效果
        document.addEventListener('touchmove', function (event) {
          if (event.scale !== 1) {
            event.preventDefault();
          }
        }, { passive: false });

        // iOS Safari 专用地址栏隐藏解决方案
        function initIOSAddressBarHiding() {
          // 设备检测
          const userAgent = navigator.userAgent;
          const isIOS = /iPad|iPhone|iPod/.test(userAgent);
          const isSafari = /^((?!chrome|android).)*safari/i.test(userAgent);
          const isPWA = window.matchMedia('(display-mode: standalone)').matches ||
                        window.navigator.standalone === true;

          console.log('🔍 设备检测:', {
            isIOS,
            isSafari,
            isPWA,
            userAgent: userAgent.substring(0, 50) + '...',
            screenHeight: screen.height,
            windowHeight: window.innerHeight,
            visualViewport: !!window.visualViewport
          });

          // 如果是PWA模式，不需要隐藏地址栏
          if (isPWA) {
            console.log('✅ PWA模式，地址栏已隐藏');
            return () => {};
          }

          // 强制全屏样式 - 移动端布局适配版
          function applyIOSFullscreenStyles() {
            // 检测是否为移动端
            const isMobileDevice = window.innerWidth < 500 ||
                                   /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

            const styles = `
              /* iOS Safari 专用全屏样式 */
              html {
                height: 100vh !important;
                height: 100dvh !important;
                width: 100vw !important;
                margin: 0 !important;
                padding: 0 !important;
                overflow: hidden !important;
                -webkit-overflow-scrolling: auto !important;
                -webkit-text-size-adjust: 100% !important;
              }

              body {
                height: 100vh !important;
                height: 100dvh !important;
                width: 100vw !important;
                margin: 0 !important;
                padding: 0 !important;
                overflow: hidden !important;
                -webkit-overflow-scrolling: auto !important;
                overscroll-behavior: none !important;
              }

              #app {
                height: 100vh !important;
                height: 100dvh !important;
                width: 100vw !important;
                overflow: hidden !important;
                position: relative !important;
                top: 0 !important;
                left: 0 !important;
              }

              /* 移动端特殊处理 - 地址栏隐藏时调整布局 */
              ${isMobileDevice ? `
                .mobile .main-content {
                  height: calc(100vh - 56px) !important;
                  height: calc(100dvh - 56px) !important;
                  height: calc(var(--vh, 1vh) * 100 - 56px) !important;
                }

                .mobile .mobile-content {
                  height: calc(100vh - 56px) !important;
                  height: calc(100dvh - 56px) !important;
                  height: calc(var(--vh, 1vh) * 100 - 56px) !important;
                }

                .mobile .app-menu {
                  bottom: 0 !important;
                  height: 56px !important;
                }

                .mobile .app-menu .app-menu-list {
                  padding-bottom: max(8px, env(safe-area-inset-bottom)) !important;
                }
              ` : ''}

              /* iOS 特殊处理 */
              @supports (-webkit-touch-callout: none) {
                html, body {
                  position: fixed !important;
                  overflow: hidden !important;
                  height: 100% !important;
                  width: 100% !important;
                }
              }
            `;

            // 移除旧的样式表
            const oldStyle = document.getElementById('ios-fullscreen-style');
            if (oldStyle) oldStyle.remove();

            const styleSheet = document.createElement('style');
            styleSheet.id = 'ios-fullscreen-style';
            styleSheet.textContent = styles;
            document.head.appendChild(styleSheet);

            // 添加地址栏隐藏标记类
            if (isMobileDevice) {
              document.documentElement.classList.add('address-bar-hidden');
              document.body.classList.add('address-bar-hidden');
              console.log('📱 已添加移动端地址栏隐藏标记类');
            }

            console.log('📱 已应用iOS全屏样式 (移动端布局适配)');
          }

          // iOS Safari 地址栏隐藏核心逻辑
          function hideIOSAddressBar() {
            console.log('🚀 开始iOS地址栏隐藏...');

            // 应用全屏样式
            applyIOSFullscreenStyles();

            // 获取当前视口信息
            const viewportHeight = window.innerHeight;
            const screenHeight = screen.height;
            const currentScroll = window.pageYOffset;

            console.log('📏 视口信息:', {
              viewportHeight,
              screenHeight,
              currentScroll,
              visualViewportHeight: window.visualViewport?.height
            });

            // 多重滚动隐藏方法
            const performHide = () => {
              // 方法1: 传统滚动方法
              if (window.pageYOffset === 0) {
                window.scrollTo(0, 1);
                console.log('📜 执行滚动隐藏: scrollTo(0, 1)');
              }

              // 方法2: 强制滚动
              setTimeout(() => {
                window.scrollTo(0, 1);
                console.log('📜 延迟滚动隐藏: scrollTo(0, 1)');
              }, 10);

              // 方法3: 使用requestAnimationFrame
              requestAnimationFrame(() => {
                window.scrollTo(0, 1);
                console.log('📜 RAF滚动隐藏: scrollTo(0, 1)');

                // 再次尝试
                setTimeout(() => {
                  window.scrollTo(0, 0);
                  console.log('📜 回滚到顶部: scrollTo(0, 0)');
                }, 50);
              });

              // 方法4: 视口高度调整
              if (window.visualViewport) {
                const vh = window.visualViewport.height;
                document.documentElement.style.setProperty('--vh', vh + 'px');
                document.documentElement.style.height = vh + 'px';
                console.log('📐 设置视口高度:', vh + 'px');
              }

              // 方法5: 强制触发事件
              setTimeout(() => {
                window.dispatchEvent(new Event('resize'));
                window.dispatchEvent(new Event('orientationchange'));
                console.log('🔄 触发resize和orientationchange事件');
              }, 100);
            };

            // 立即执行
            performHide();

            // 多次延迟执行
            const delays = [50, 100, 200, 300, 500, 800, 1000, 1500, 2000];
            delays.forEach(delay => {
              setTimeout(() => {
                console.log(`⏰ 延迟${delay}ms执行地址栏隐藏`);
                performHide();
              }, delay);
            });
          }

          return hideIOSAddressBar;
        }

        // 初始化iOS地址栏隐藏功能
        const hideIOSAddressBar = initIOSAddressBarHiding();

        // 立即执行
        console.log('🚀 立即执行iOS地址栏隐藏');
        hideIOSAddressBar();

        // 页面加载完成后执行
        window.addEventListener('load', function() {
          console.log('📄 页面加载完成，执行地址栏隐藏');
          setTimeout(hideIOSAddressBar, 100);
          setTimeout(hideIOSAddressBar, 500);
        });

        // DOM加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
          console.log('🏗️ DOM加载完成，执行地址栏隐藏');
          hideIOSAddressBar();
        });

        // 用户交互后执行 - 增强版
        let interactionCount = 0;
        let lastInteractionTime = 0;

        function handleUserInteraction(event) {
          const now = Date.now();
          interactionCount++;

          // 防止过于频繁的执行
          if (now - lastInteractionTime < 100) return;
          lastInteractionTime = now;

          console.log(`👆 用户交互 #${interactionCount} (${event.type})，执行地址栏隐藏`);
          hideIOSAddressBar();

          // 前几次交互多次尝试
          if (interactionCount <= 5) {
            setTimeout(hideIOSAddressBar, 200);
            setTimeout(hideIOSAddressBar, 500);
            setTimeout(hideIOSAddressBar, 1000);
          }
        }

        // 监听多种交互事件
        document.addEventListener('touchstart', handleUserInteraction, { passive: true });
        document.addEventListener('touchend', handleUserInteraction, { passive: true });
        document.addEventListener('touchmove', handleUserInteraction, { passive: true });
        document.addEventListener('click', handleUserInteraction);
        document.addEventListener('scroll', handleUserInteraction, { passive: true });
        document.addEventListener('gesturestart', handleUserInteraction, { passive: true });
        document.addEventListener('gestureend', handleUserInteraction, { passive: true });

        // 方向变化时执行 - 增强版
        window.addEventListener('orientationchange', function() {
          console.log('🔄 屏幕方向变化，执行地址栏隐藏');

          // 方向变化需要更多延迟
          const delays = [100, 300, 500, 800, 1000, 1500, 2000];
          delays.forEach(delay => {
            setTimeout(() => {
              console.log(`🔄 方向变化延迟${delay}ms执行`);
              hideIOSAddressBar();
            }, delay);
          });
        });

        // 窗口大小变化时执行
        window.addEventListener('resize', function() {
          console.log('📏 窗口大小变化，执行地址栏隐藏');
          setTimeout(hideIOSAddressBar, 100);
          setTimeout(hideIOSAddressBar, 300);
        });

        // 视口变化监听 (iOS 15+)
        if (window.visualViewport) {
          window.visualViewport.addEventListener('resize', function() {
            console.log('📐 视口大小变化，执行地址栏隐藏');
            hideIOSAddressBar();
          });

          window.visualViewport.addEventListener('scroll', function() {
            console.log('📐 视口滚动变化，执行地址栏隐藏');
            hideIOSAddressBar();
          });
        }

        // 页面可见性变化时执行
        document.addEventListener('visibilitychange', function() {
          if (!document.hidden) {
            console.log('👁️ 页面变为可见，执行地址栏隐藏');
            setTimeout(hideIOSAddressBar, 300);
            setTimeout(hideIOSAddressBar, 800);
          }
        });

        // 窗口获得焦点时执行
        window.addEventListener('focus', function() {
          console.log('🎯 窗口获得焦点，执行地址栏隐藏');
          setTimeout(hideIOSAddressBar, 200);
          setTimeout(hideIOSAddressBar, 600);
        });

        // 页面滚动监听
        let scrollTimeout;
        window.addEventListener('scroll', function() {
          clearTimeout(scrollTimeout);
          scrollTimeout = setTimeout(() => {
            if (window.pageYOffset === 0) {
              console.log('📜 滚动到顶部，执行地址栏隐藏');
              hideIOSAddressBar();
            }
          }, 100);
        }, { passive: true });

        // 定期检查和执行（前60秒内）
        let checkCount = 0;
        const intervalCheck = setInterval(function() {
          checkCount++;
          console.log(`⏰ 定期检查 #${checkCount}，执行地址栏隐藏`);
          hideIOSAddressBar();

          if (checkCount >= 20) { // 20次后停止定期检查
            clearInterval(intervalCheck);
            console.log('⏹️ 停止定期检查');
          }
        }, 3000); // 每3秒检查一次

        // 页面卸载前清理
        window.addEventListener('beforeunload', function() {
          clearInterval(intervalCheck);
        });
      }

      // PWA 安装提示
      let deferredPrompt;
      window.addEventListener('beforeinstallprompt', (e) => {
        e.preventDefault();
        deferredPrompt = e;
        console.log('PWA 安装提示已准备');
      });

      // 检测是否在PWA模式下运行
      function isPWA() {
        return window.matchMedia('(display-mode: standalone)').matches ||
               window.navigator.standalone === true;
      }

      // PWA模式检测和初始化
      function initPWAMode() {
        const isPWAMode = isPWA();

        if (isPWAMode) {
          document.documentElement.classList.add('pwa-mode');
          document.body.classList.add('pwa-mode');
          console.log('运行在PWA模式下');

          // PWA模式下的特殊处理
          document.documentElement.style.height = '100vh';
          document.documentElement.style.overflow = 'hidden';
          document.body.style.height = '100vh';
          document.body.style.overflow = 'hidden';
          document.body.style.position = 'fixed';
          document.body.style.width = '100%';
          document.body.style.top = '0';
          document.body.style.left = '0';
        } else {
          console.log('运行在浏览器模式下');
        }

        // 添加设备类型类
        if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
          document.documentElement.classList.add('ios-device');
        }

        if (/^((?!chrome|android).)*safari/i.test(navigator.userAgent)) {
          document.documentElement.classList.add('safari-browser');
        }
      }

      // 立即执行PWA初始化
      initPWAMode();

      // DOM加载完成后再次检查
      document.addEventListener('DOMContentLoaded', initPWAMode);
    </script>
  </body>
</html>
