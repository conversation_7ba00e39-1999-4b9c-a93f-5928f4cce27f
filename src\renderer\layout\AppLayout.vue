<template>
  <div class="layout-page">
    <div id="layout-main" class="layout-main">
      <title-bar />
      <div class="layout-main-page">
        <!-- 侧边菜单栏 -->
        <app-menu v-if="!isMobile" class="menu" :menus="menus" />
        <div class="main">
          <!-- 搜索栏 -->
          <search-bar />
          <!-- 主页面路由 -->
          <div class="main-content" :native-scrollbar="false" :class="{'mobile-content': !shouldShowMobileMenu}">
            <router-view
              v-slot="{ Component }"
              class="main-page"
              :class="route.meta.noScroll && !isMobile ? 'pr-3' : ''"
            >
              <keep-alive :include="keepAliveInclude">
                <component :is="Component" />
              </keep-alive>
            </router-view>
          </div>
          <play-bottom />
          <app-menu v-if="shouldShowMobileMenu" class="menu" :menus="menus" />
        </div>
      </div>
      <!-- 底部音乐播放 -->
      <template v-if="!settingsStore.isMiniMode">
        <play-bar
          v-if="!isMobile"
          v-show="isPlay"
          :style="playerStore.musicFull ? 'bottom: 0;' : ''"
        />
        <mobile-play-bar
          v-else
          v-show="isPlay"
          :style="isMobile && playerStore.musicFull ? 'bottom: 0;' : ''"
        />
      </template>
    </div>
    <install-app-modal v-if="!isElectron"></install-app-modal>
    <update-modal v-if="isElectron" />
    <playlist-drawer v-model="showPlaylistDrawer" :song-id="currentSongId" />
    <SleepTimerTop v-if="!isMobile"/>
    <!-- 下载管理抽屉 -->
    <download-drawer
      v-if="
        isElectron &&
        (settingsStore.setData?.alwaysShowDownloadButton ||
          settingsStore.showDownloadDrawer ||
          settingsStore.setData?.hasDownloadingTasks)
      "
    />
    <!-- 播放列表抽屉 -->
    <playing-list-drawer />
  </div>
</template>

<script lang="ts" setup>
import { computed, defineAsyncComponent, onMounted, provide, ref } from 'vue';
import { useRoute } from 'vue-router';

import DownloadDrawer from '@/components/common/DownloadDrawer.vue';
import InstallAppModal from '@/components/common/InstallAppModal.vue';
import PlayBottom from '@/components/common/PlayBottom.vue';
import UpdateModal from '@/components/common/UpdateModal.vue';
import homeRouter from '@/router/home';
import otherRouter from '@/router/other';
import { useMenuStore } from '@/store/modules/menu';
import { usePlayerStore } from '@/store/modules/player';
import { useSettingsStore } from '@/store/modules/settings';
import { isElectron, isMobile } from '@/utils';
import SleepTimerTop from '@/components/player/SleepTimerTop.vue';

const keepAliveInclude = computed(() => {
  const allRoutes = [...homeRouter, ...otherRouter];
  
  return allRoutes
    .filter((item) => {
      return item.meta?.keepAlive;
    })
    .map((item) => {
      return typeof item.name === 'string' 
        ? item.name.charAt(0).toUpperCase() + item.name.slice(1) 
        : '';
    })
    .filter(Boolean);
});

const AppMenu = defineAsyncComponent(() => import('./components/AppMenu.vue'));
const PlayBar = defineAsyncComponent(() => import('@/components/player/PlayBar.vue'));
const MobilePlayBar = defineAsyncComponent(() => import('@/components/player/MobilePlayBar.vue'));
const SearchBar = defineAsyncComponent(() => import('./components/SearchBar.vue'));
const TitleBar = defineAsyncComponent(() => import('./components/TitleBar.vue'));
const PlayingListDrawer = defineAsyncComponent(() => import('@/components/player/PlayingListDrawer.vue'));
const PlaylistDrawer = defineAsyncComponent(() => import('@/components/common/PlaylistDrawer.vue'));

const playerStore = usePlayerStore();
const settingsStore = useSettingsStore();
const menuStore = useMenuStore();

const isPlay = computed(() => playerStore.playMusic && playerStore.playMusic.id);
const { menus } = menuStore;
const route = useRoute();

// 判断当前路由是否应该在移动端显示AppMenu
const shouldShowMobileMenu = computed(() => {
  // 在移动端始终显示底部导航，除非是全屏播放模式
  if (!isMobile.value) return false;

  // 全屏播放时隐藏底部导航
  if (playerStore.musicFull) return false;

  // 特殊路由不显示底部导航
  const excludeRoutes = ['/lyric', '/mini'];
  if (excludeRoutes.includes(route.path)) return false;

  // 其他情况都显示底部导航
  return true;
});

provide('shouldShowMobileMenu', shouldShowMobileMenu);

onMounted(() => {
  settingsStore.initializeSettings();
  settingsStore.initializeTheme();
});

const showPlaylistDrawer = ref(false);
const currentSongId = ref<number | undefined>();

// 提供一个方法来打开歌单抽屉
const openPlaylistDrawer = (songId: number, isOpen: boolean = true) => {
  currentSongId.value = songId;
  showPlaylistDrawer.value = isOpen;
  playerStore.setMusicFull(false);
  playerStore.setPlayListDrawerVisible(!isOpen);
};

// 将方法提供给全局
provide('openPlaylistDrawer', openPlaylistDrawer);
</script>

<style lang="scss" scoped>
.layout-page {
  @apply w-screen h-screen overflow-hidden bg-light dark:bg-black;
}

.layout-main {
  @apply w-full h-full relative  text-gray-900 dark:text-white;
}

.layout-main-page {
  @apply flex h-full;
}

.menu {
  @apply h-full;
}

.main {
  @apply overflow-hidden flex-1 flex flex-col;
}

.main-content {
  @apply flex-1 overflow-hidden;
}

.main-page {
  @apply h-full;
}

.mobile {
  /* 修复移动端内容显示问题 */
  .main-content {
    height: calc(100vh - 60px);
    width: 100%;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    padding: 0;
    padding-bottom: calc(60px + env(safe-area-inset-bottom, 0px));
    scrollbar-width: none;
    position: relative;
    z-index: 1;
    box-sizing: border-box;
  }

  .main-content::-webkit-scrollbar {
    display: none;
  }

  .mobile-content {
    height: calc(100vh - 60px);
    width: 100%;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    padding: 0;
    padding-bottom: calc(60px + env(safe-area-inset-bottom, 0px));
    scrollbar-width: none;
    position: relative;
    z-index: 1;
    box-sizing: border-box;
  }

  .mobile-content::-webkit-scrollbar {
    display: none;
  }

  /* PWA模式特殊样式 */
  @media screen and (display-mode: standalone),
         screen and (display-mode: fullscreen) {
    .main-content,
    .mobile-content {
      height: calc(100vh - 60px - env(safe-area-inset-bottom, 0px));
      padding-bottom: calc(60px + env(safe-area-inset-bottom, 0px));
    }
  }

  /* 性能优化 */
  .main-content,
  .mobile-content {
    will-change: scroll-position;
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
  }
}
</style>
