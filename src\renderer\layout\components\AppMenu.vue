<template>
  <div>
    <!-- menu -->
    <div class="app-menu" :class="{ 'app-menu-expanded': isText }">
      <div class="app-menu-header">
        <div class="app-menu-logo" @click="isText = !isText">
          <img :src="icon" class="w-9 h-9" alt="logo" />
        </div>
      </div>
      <div class="app-menu-list">
        <div v-for="(item, index) in filteredMenus" :key="item.path" class="app-menu-item">
          <n-tooltip :delay="200" :disabled="isText || isMobile" placement="bottom">
            <template #trigger>
              <router-link class="app-menu-item-link" :to="item.path">
                <i class="iconfont app-menu-item-icon" :style="iconStyle(item, index)" :class="item.meta.icon"></i>
                <span v-if="isText" class="app-menu-item-text ml-3" :class="isChecked(item.path) ? 'text-green-500' : ''">{{ t(item.meta.title) }}</span>
              </router-link>
            </template>
            <div v-if="!isText">{{ t(item.meta.title) }}</div>
          </n-tooltip>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useRoute } from 'vue-router';
import { ref, watch, computed } from 'vue';
import { useI18n } from 'vue-i18n';

import icon from '@/assets/icon.png';
import { isMobile } from '@/utils';

const props = defineProps({
  size: {
    type: String,
    default: '26px'
  },
  color: {
    type: String,
    default: '#aaa'
  },
  selectColor: {
    type: String,
    default: '#10B981'
  },
  menus: {
    type: Array as any,
    default: () => []
  }
});

const route = useRoute();
const path = ref(route.path);
watch(
  () => route.path,
  async (newParams) => {
    path.value = newParams;
  }
);

const { t } = useI18n();

// 过滤菜单 - 移动端只显示标记为移动端的菜单项
const filteredMenus = computed(() => {
  if (isMobile.value) {
    return props.menus.filter((item: any) => item.meta?.isMobile === true);
  }
  return props.menus;
});

const isChecked = (itemPath: string) => {
  return path.value === itemPath;
};

const iconStyle = (item: any, index: number) => {
  const style = {
    fontSize: props.size,
    color: isChecked(item.path) ? props.selectColor : props.color
  };
  return style;
};

const isText = ref(false);
</script>

<style lang="scss" scoped>
.app-menu {
  @apply flex-col items-center justify-center transition-all duration-300 w-[100px] px-1;
}

.app-menu-expanded {
  @apply w-[160px];

  .app-menu-item {
    @apply hover:bg-gray-100 dark:hover:bg-gray-800 rounded mr-4;
  }
}

.app-menu-item-link,
.app-menu-header {
  @apply flex items-center w-[200px] overflow-hidden ml-2 px-5;
}

.app-menu-header {
  @apply ml-1;
}

.app-menu-item-link {
  @apply mb-6 mt-6;
}

.app-menu-item-icon {
  @apply transition-all duration-200 text-gray-500 dark:text-gray-400;

  &:hover {
    @apply text-green-500 scale-105 !important;
  }
}

.mobile {
  .app-menu {
    /* 参考完美PWA网站的底部导航栏样式 */
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100vw;
    height: 60px;
    padding-bottom: env(safe-area-inset-bottom, 0px);
    background: rgba(18, 18, 18, 0.98);
    z-index: 1000;
    border-top: 1px solid rgba(51, 51, 51, 0.2);
    box-shadow: 0 -1px 8px rgba(0, 0, 0, 0.2);

    &-header {
      display: none;
    }

    &-list {
      display: flex;
      justify-content: space-around;
      align-items: center;
      height: 100%;
      padding: 0;
    }

    &-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      color: rgba(255, 255, 255, 0.6);
      font-size: 12px;
      padding: 8px 0;
      transition: all 0.3s;
      cursor: pointer;
      width: 25%;

      &-link {
        display: flex;
        flex-direction: column;
        align-items: center;
        color: inherit;
        text-decoration: none;
        width: 100%;
        padding: 0;
        margin: 0;
      }

      &-icon {
        font-size: 24px;
        margin-bottom: 4px;
        transition: all 0.3s;
      }

      &-text {
        font-size: 12px;
        font-weight: normal;
      }

      /* 激活状态 - 参考完美PWA网站 */
      &.router-link-active,
      &:has(.router-link-active) {
        color: #10B981;
        transform: scale(1.05);
        font-weight: 500;
        text-shadow: 0 0 10px rgba(16, 185, 129, 0.4);

        .app-menu-item-icon {
          text-shadow: 0 0 15px rgba(16, 185, 129, 0.6);
        }
      }
    }

    &-expanded {
      @apply w-full;
    }
  }

  /* 深色模式适配 */
  @media (prefers-color-scheme: dark) {
    .app-menu {
      background: rgba(18, 18, 18, 0.98);
      border-top: 1px solid rgba(51, 51, 51, 0.2);
    }
  }

  /* 浅色模式适配 */
  @media (prefers-color-scheme: light) {
    .app-menu {
      background: rgba(255, 255, 255, 0.98);
      border-top: 1px solid rgba(0, 0, 0, 0.1);

      .app-menu-item {
        color: rgba(0, 0, 0, 0.6);
      }
    }
  }

  /* PWA模式特殊样式 */
  @media screen and (display-mode: standalone),
         screen and (display-mode: fullscreen) {
    .app-menu {
      bottom: 0;
    }
  }

  /* 性能优化 */
  .app-menu {
    will-change: transform;
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
  }
}
</style>
