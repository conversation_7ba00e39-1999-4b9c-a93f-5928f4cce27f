<template>
  <div>
    <!-- menu -->
    <div class="app-menu" :class="{ 'app-menu-expanded': isText }">
      <div class="app-menu-header">
        <div class="app-menu-logo" @click="isText = !isText">
          <img :src="icon" class="w-9 h-9" alt="logo" />
        </div>
      </div>
      <div class="app-menu-list">
        <div v-for="(item, index) in menus" :key="item.path" class="app-menu-item">
          <n-tooltip :delay="200" :disabled="isText || isMobile" placement="bottom">
            <template #trigger>
              <router-link class="app-menu-item-link" :to="item.path">
                <i class="iconfont app-menu-item-icon" :style="iconStyle(index)" :class="item.meta.icon"></i>
                <span v-if="isText" class="app-menu-item-text ml-3" :class="isChecked(index) ? 'text-green-500' : ''">{{ t(item.meta.title) }}</span>
              </router-link>
            </template>
            <div v-if="!isText">{{ t(item.meta.title) }}</div>
          </n-tooltip>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useRoute } from 'vue-router';
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';

import icon from '@/assets/icon.png';
import { isMobile } from '@/utils';

const props = defineProps({
  size: {
    type: String,
    default: '26px'
  },
  color: {
    type: String,
    default: '#aaa'
  },
  selectColor: {
    type: String,
    default: '#10B981'
  },
  menus: {
    type: Array as any,
    default: () => []
  }
});

const route = useRoute();
const path = ref(route.path);
watch(
  () => route.path,
  async (newParams) => {
    path.value = newParams;
  }
);

const { t } = useI18n();

const isChecked = (index: number) => {
  return path.value === props.menus[index].path;
};

const iconStyle = (index: number) => {
  const style = {
    fontSize: props.size,
    color: isChecked(index) ? props.selectColor : props.color
  };
  return style;
};

const isText = ref(false);
</script>

<style lang="scss" scoped>
.app-menu {
  @apply flex-col items-center justify-center transition-all duration-300 w-[100px] px-1;
}

.app-menu-expanded {
  @apply w-[160px];

  .app-menu-item {
    @apply hover:bg-gray-100 dark:hover:bg-gray-800 rounded mr-4;
  }
}

.app-menu-item-link,
.app-menu-header {
  @apply flex items-center w-[200px] overflow-hidden ml-2 px-5;
}

.app-menu-header {
  @apply ml-1;
}

.app-menu-item-link {
  @apply mb-6 mt-6;
}

.app-menu-item-icon {
  @apply transition-all duration-200 text-gray-500 dark:text-gray-400;

  &:hover {
    @apply text-green-500 scale-105 !important;
  }
}

.mobile {
  .app-menu {
    max-width: 100%;
    width: 100vw;
    width: 100dvw;
    position: fixed;
    bottom: 0;
    bottom: max(0px, env(safe-area-inset-bottom));
    left: 0;
    z-index: 99999;
    @apply bg-light dark:bg-black border-t border-gray-200 dark:border-gray-700;

    /* iOS Safari 地址栏隐藏适配 */
    transition: all 0.3s ease;

    &-header {
      display: none;
    }

    &-list {
      @apply flex justify-between px-4;
      padding-bottom: max(12px, env(safe-area-inset-bottom));
    }

    &-item {
      @apply flex-1 flex justify-center;

      &-link {
        @apply my-3 w-auto px-2 flex flex-col items-center justify-center;
        width: auto !important;
      }

      &-icon {
        @apply text-[32px] mb-1;
      }

      &-text {
        @apply text-xs font-medium;
      }
    }

    &-expanded {
      @apply w-full;
    }
  }

  /* iOS Safari 地址栏隐藏时的特殊样式 */
  &.address-bar-hidden {
    .app-menu {
      /* 地址栏隐藏时，tabbar可以更贴近底部 */
      bottom: 0 !important;

      &-list {
        padding-bottom: max(8px, env(safe-area-inset-bottom));
      }
    }
  }
}
</style>
