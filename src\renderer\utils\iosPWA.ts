/**
 * iOS PWA 专用优化工具
 * 确保在iPad和iPhone上添加到主屏幕后显示为原生应用
 */

export class IOSPWAOptimizer {
  private static instance: IOSPWAOptimizer;
  
  public static getInstance(): IOSPWAOptimizer {
    if (!IOSPWAOptimizer.instance) {
      IOSPWAOptimizer.instance = new IOSPWAOptimizer();
    }
    return IOSPWAOptimizer.instance;
  }

  /**
   * 检测是否为iOS设备
   */
  public isIOSDevice(): boolean {
    return /iPad|iPhone|iPod/.test(navigator.userAgent);
  }

  /**
   * 检测是否为PWA模式（已添加到主屏幕）
   */
  public isIOSPWA(): boolean {
    return (window.navigator as any).standalone === true;
  }

  /**
   * 检测是否为Safari浏览器
   */
  public isSafari(): boolean {
    return /Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent);
  }

  /**
   * 初始化iOS PWA优化
   */
  public init(): void {
    if (!this.isIOSDevice()) {
      return;
    }

    console.log('iOS设备检测到，开始PWA优化...');
    console.log('PWA模式:', this.isIOSPWA());
    console.log('Safari浏览器:', this.isSafari());

    // 添加设备类型标识
    document.documentElement.classList.add('ios-device');
    
    if (this.isIOSPWA()) {
      // PWA模式：隐藏Safari界面
      document.documentElement.classList.add('ios-pwa-mode');
      document.body.classList.add('pwa-mode');
      this.setupPWAMode();
    } else if (this.isSafari()) {
      // Safari浏览器模式：优化显示
      document.documentElement.classList.add('ios-safari-mode');
      this.setupSafariMode();
    }

    // 通用iOS优化
    this.setupIOSOptimizations();
  }

  /**
   * 设置PWA模式优化
   */
  private setupPWAMode(): void {
    console.log('设置iOS PWA模式...');
    
    // 强制全屏
    this.forceFullscreen();
    
    // 防止页面滚动
    this.preventPageScroll();
    
    // 设置状态栏样式
    this.setupStatusBar();
    
    // 添加PWA特定样式
    this.addPWAStyles();
  }

  /**
   * 设置Safari模式优化
   */
  private setupSafariMode(): void {
    console.log('设置iOS Safari模式...');
    
    // Safari特定优化
    this.setupSafariOptimizations();
  }

  /**
   * 强制全屏显示
   */
  private forceFullscreen(): void {
    // 设置视口
    const viewport = document.querySelector('meta[name="viewport"]');
    if (viewport) {
      viewport.setAttribute('content', 
        'width=device-width, height=device-height, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover'
      );
    }

    // 强制全屏样式
    const style = document.createElement('style');
    style.textContent = `
      html.ios-pwa-mode {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        height: 100dvh !important;
        overflow: hidden !important;
        margin: 0 !important;
        padding: 0 !important;
      }
      
      html.ios-pwa-mode body {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        height: 100dvh !important;
        overflow: hidden !important;
        margin: 0 !important;
        padding: 0 !important;
        -webkit-overflow-scrolling: touch !important;
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * 防止页面滚动
   */
  private preventPageScroll(): void {
    document.addEventListener('touchmove', (e) => {
      if (e.target === document.body) {
        e.preventDefault();
      }
    }, { passive: false });
  }

  /**
   * 设置状态栏样式
   */
  private setupStatusBar(): void {
    // 确保状态栏样式正确
    let statusBarMeta = document.querySelector('meta[name="apple-mobile-web-app-status-bar-style"]');
    if (!statusBarMeta) {
      statusBarMeta = document.createElement('meta');
      statusBarMeta.setAttribute('name', 'apple-mobile-web-app-status-bar-style');
      document.head.appendChild(statusBarMeta);
    }
    statusBarMeta.setAttribute('content', 'black-translucent');
  }

  /**
   * 添加PWA专用样式
   */
  private addPWAStyles(): void {
    const style = document.createElement('style');
    style.textContent = `
      .ios-pwa-mode #app {
        position: relative !important;
        width: 100vw !important;
        height: 100vh !important;
        height: 100dvh !important;
        overflow: hidden !important;
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * Safari浏览器优化
   */
  private setupSafariOptimizations(): void {
    // Safari特定的优化
    const style = document.createElement('style');
    style.textContent = `
      .ios-safari-mode {
        -webkit-text-size-adjust: 100%;
        -webkit-tap-highlight-color: transparent;
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * 通用iOS优化
   */
  private setupIOSOptimizations(): void {
    // 防止双击缩放
    let lastTouchEnd = 0;
    document.addEventListener('touchend', (event) => {
      const now = (new Date()).getTime();
      if (now - lastTouchEnd <= 300) {
        event.preventDefault();
      }
      lastTouchEnd = now;
    }, false);

    // 防止长按选择
    document.addEventListener('selectstart', (e) => e.preventDefault());
    document.addEventListener('contextmenu', (e) => e.preventDefault());

    // 优化触摸滚动
    document.addEventListener('touchstart', () => {}, { passive: true });
    document.addEventListener('touchmove', () => {}, { passive: true });
  }

  /**
   * 检查PWA安装状态
   */
  public checkPWAInstallation(): void {
    if (this.isIOSDevice() && !this.isIOSPWA()) {
      console.log('iOS设备未安装PWA，建议添加到主屏幕以获得最佳体验');
    }
  }
}

// 自动初始化
document.addEventListener('DOMContentLoaded', () => {
  const optimizer = IOSPWAOptimizer.getInstance();
  optimizer.init();
  optimizer.checkPWAInstallation();
});

// 导出实例
export const iosPWAOptimizer = IOSPWAOptimizer.getInstance();
